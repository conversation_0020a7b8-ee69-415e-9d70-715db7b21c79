## Name of the module
Azure Subnet Module Tools

Shortname: snet-ttols

Terraform resource: -

## Short description of the module
This Terraform module returns next free Azure Subnet CIDR(s)

## [USAGE.md](USAGE.md) 

## Detailed description on Confluence
[Azure Networking Modules](https://confluence.otpbank.hu/x/_EyUKg)

## Terraform version compatibility
Terraform v1.3.6

## Necessary Terraform providers, and compatibility to provider versions
provider registry.terraform.io/hashicorp/azurerm >= 3.30.0, < 4.0.0

## Resources generated by the module
- None

## Description of sample code
```hcl
module "subnet_tools" {
  source                 = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/tooling//subnet_tools?ref=v2"
  vnet_name              = data.azurerm_virtual_network.dev-vn.name
  vnet_rgrp_name         = data.azurerm_virtual_network.dev-vn.resource_group_name
  subnet_prefixes_length = [28, 29, 28, 29]
}
```