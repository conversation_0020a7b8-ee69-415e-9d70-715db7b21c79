variable "vnet_name" {
  type        = string
  description = "(Required) The name of the virtual network in which to create the subnet."
}

variable "vnet_rgrp_name" {
  type        = string
  description = "(Required) The resource group name of the resource groupt witch contains the virtual network in which to create the subnet."
}

variable "subnet_prefixes_length" {
  type        = list(number)
  description = "(Required) The address prefixes lenght to use for the subnet."
}
