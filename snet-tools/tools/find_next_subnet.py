# -*- coding: utf-8 -*-
import argparse, os, ipaddress, json, sys

def find_next_subnets(virtual_networks, existing_subnets, subnet_prefixes_length):
    # print(existing_subnets)
    for subnet_prefix_length in subnet_prefixes_length:
        for virtual_network_address_space in virtual_networks:
            ip_adresses = [str(ip) for ip in ipaddress.ip_network(virtual_network_address_space).hosts()]
            for ip in ip_adresses:
                new_subnet = ipaddress.ip_network(ip + '/' + subnet_prefix_length, strict=False)
                overlap = False
                for subnet in existing_subnets:
                    if (new_subnet.overlaps(ipaddress.ip_network(subnet))):
                        overlap = True
                        break
                if (not overlap):
                    if(len(subnet_prefixes_length) > 1):
                        existing_subnets.append(str(new_subnet))
                        subnet_prefixes_length.pop(0)
                        return str(new_subnet) + " " + (str(find_next_subnets(virtual_networks, existing_subnets, subnet_prefixes_length)))
                    else:
                        return str(new_subnet)

if (len(sys.argv) > 1):
    # json_obj = json.loads(sys.argv[1])
    parser = argparse.ArgumentParser()
    parser.add_argument('-n','--virtual_networks', nargs='+', help='<Required> Virtual networks', required=False)
    parser.add_argument('-s','--existing_subnets', nargs='+', help='<Required> Existing subnets', required=False)
    parser.add_argument('-l','--subnet_prefixes_length', nargs='+', help='<Required> subnet prefix length', required=False)
    args = parser.parse_args()
    virtual_networks = args.virtual_networks
    existing_subnets = args.existing_subnets
    subnet_prefixes_length = args.subnet_prefixes_length
else:
    json_obj = json.loads(input())
    virtual_networks = json_obj['virtual_networks'].split()
    existing_subnets = json_obj['existing_subnets'].split()
    subnet_prefixes_length = json_obj['subnet_prefixes_length'].split()

next_subnet = find_next_subnets(virtual_networks, existing_subnets, subnet_prefixes_length)

print(json.dumps({"next_subnets": next_subnet}))
