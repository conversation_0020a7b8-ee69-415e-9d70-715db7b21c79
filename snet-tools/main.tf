data "azurerm_virtual_network" "vnet_dest" {
  name                = var.vnet_name
  resource_group_name = var.vnet_rgrp_name
}

data "azurerm_subnet" "subnets" {
  count = length(data.azurerm_virtual_network.vnet_dest.subnets)

  name                 = data.azurerm_virtual_network.vnet_dest.subnets[count.index]
  resource_group_name  = var.vnet_rgrp_name
  virtual_network_name = var.vnet_name
}

data "external" "find_next_subnet" {
  program = ["python3", "${path.module}/tools/find_next_subnet.py"]
  query = {
    virtual_networks       = join(" ", data.azurerm_virtual_network.vnet_dest.address_space)
    existing_subnets       = join(" ", toset(data.azurerm_subnet.subnets[*].address_prefix)) # Currently only a single address prefix can be set as the Multiple Subnet Address prefix Feature is not yet in public preview or general availability.
    subnet_prefixes_length = join(" ", var.subnet_prefixes_length)
  }
}
