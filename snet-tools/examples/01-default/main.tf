data "azurerm_virtual_network" "dev-vn" {
  name                = "otp-dd-coeinfdev-sub-dev-01-vnet-northeu-01"
  resource_group_name = "otp-dd-coeinfdev-sub-dev-01-rg-northeu-01"
}

data "azurerm_route_table" "deault_rtbl" {
  name                = "otp-dd-coeinfdev-sub-dev-01-rt-northeu-01"
  resource_group_name = "otp-dd-coeinfdev-sub-dev-01-rg-northeu-01"
}

module "subnet_tools" {
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  //source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-network//snet-tools?ref=v1.2.5"
  source                 = "../.."
  vnet_name              = data.azurerm_virtual_network.dev-vn.name
  vnet_rgrp_name         = data.azurerm_virtual_network.dev-vn.resource_group_name
  subnet_prefixes_length = [28, 28, 29]
}

output "debug" {
  value = module.subnet_tools.next_subnets
}
