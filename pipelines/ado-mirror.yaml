trigger:
- none

parameters:
- name: cleanup
  displayName: Remove Mirror Repositories
  type: boolean
  default: false
- name: tfpatch
  displayName: Patch the module sources
  type: boolean
  default: true

variables:
- group: tf-mirror

pool:
  vmImage: ubuntu-latest

stages:
  - stage: InventoryStage
    condition: eq('${{ parameters.cleanup }}','False')
    jobs:
    - job: InventoryJob
      steps:
      - task: Bash@3
        name: RepoInventory
        inputs:
          targetType: 'inline'
          script: |
            REPOJSON=$(az repos list --org https://dev.azure.com/$(SOURCE_ORG)/ -p $(SOURCE_PROJECT) | jq -c '[.[] | select(.name? | match("$(SOURCE_FILTER_REGEX)")) | select(.isDisabled|not) ] | map({(.name): {"name"}}) | add')
            echo "##vso[task.setvariable variable=RepoJson;isoutput=true]$REPOJSON"
        env:
          AZURE_DEVOPS_EXT_PAT: $(SOURCE_PAT)

  - stage: GitSyncStage
    condition: eq('${{ parameters.cleanup }}','False')
    dependsOn: InventoryStage
    jobs:
    - job:
      strategy:
        matrix: $[ stageDependencies.InventoryStage.InventoryJob.outputs['RepoInventory.RepoJson'] ]
      steps:
      - task: Bash@3
        displayName: GitSync-$(name)
        inputs:
          targetType: 'inline'
          script: |
            set -e
            trap "echo Error on line $LINENO" ERR
            echo "Repo name: $(name)"
            # Setup
            REPOSITORY_URL_SUFFIX=$(TARGET_ORG)/$(TARGET_PROJECT)/_git/$(TARGET_PREFIX)$(name)/
            mkdir src
            mkdir dst
            cd src

            # check if the remote exits
            REPO_INFO=$(az repos list --org https://dev.azure.com/$(TARGET_ORG)/ --project $(TARGET_PROJECT) | jq '.[] | select(.name=="$(TARGET_PREFIX)$(name)")')

            # Unset this so it can be set later
            # git config --unset remote.origin.fetch

            export GIT_CONFIG_COUNT=3
            # Define authentication with PAT
            export GIT_CONFIG_KEY_0="url.https://$(SOURCE_ORG):$(SOURCE_PAT)@dev.azure.com/$(SOURCE_ORG)/$(SOURCE_PROJECT)/_git/$(name)/.insteadOf"
            export GIT_CONFIG_VALUE_0="https://dev.azure.com/$(SOURCE_ORG)/$(SOURCE_PROJECT)/_git/$(name)/"
            # Git repository mirroring would try to mirror PRs (refs/heads/pull) and would fail in case of active PRs. These configs below define what to mirror.
            export GIT_CONFIG_KEY_1="remote.origin.fetch"
            export GIT_CONFIG_VALUE_1="+refs/heads/*:refs/remotes/origin/heads/*"
            export GIT_CONFIG_KEY_2="remote.origin.fetch"
            export GIT_CONFIG_VALUE_2="+refs/tags/*:refs/remotes/origin/tags/*"

            if [[ "${{ parameters.tfpatch }}" == "False" ]]
            then
              # if we are not patching the repo we need to clone as bare (mirror) repo
              echo "Cloning source repository"
              git clone --mirror https://dev.azure.com/$(SOURCE_ORG)/$(SOURCE_PROJECT)/_git/$(name)/
              cd $(name).git
            else
              # if we paching the repo we need to clone as normal repo
              echo "Cloning source repository"
              git clone https://dev.azure.com/$(SOURCE_ORG)/$(SOURCE_PROJECT)/_git/$(name)/
              cd $(name)
            fi

            echo "Switch to target"
            # setup the repo for further processing
            git config --unset remote.origin.fetch || true
            # config push operation
            export GIT_CONFIG_COUNT=3
            # Define authentication with PAT
            export GIT_CONFIG_KEY_0="url.https://$(TARGET_ORG):$(TARGET_PAT)@dev.azure.com/$REPOSITORY_URL_SUFFIX.insteadOf"
            export GIT_CONFIG_VALUE_0="https://dev.azure.com/$REPOSITORY_URL_SUFFIX"
            # Since the fetch operation of the mirroring was overriden above, the push needs to be overriden as well.
            export GIT_CONFIG_KEY_1="remote.origin.push"
            export GIT_CONFIG_VALUE_1="+refs/remotes/origin/heads/*:refs/heads/*"
            export GIT_CONFIG_KEY_2="remote.origin.push"
            export GIT_CONFIG_VALUE_2="+refs/remotes/origin/tags/*:refs/tags/*"
            # Set remote URL so that the push config defined above works well.
            git remote set-url --push origin "https://dev.azure.com/$REPOSITORY_URL_SUFFIX"

            if [[ "$REPO_INFO" == "" ]]
            then
              # create repository if not exists
              echo "Creating repository: https://dev.azure.com/$REPOSITORY_URL_SUFFIX"
              az repos create --name "$(TARGET_PREFIX)$(name)" --org https://dev.azure.com/$(TARGET_ORG)/ --project $(TARGET_PROJECT)
              cd ../../dst
              git clone https://dev.azure.com/$REPOSITORY_URL_SUFFIX
              cd $(TARGET_PREFIX)$(name)
              git config --global user.email "$(Build.QueuedById)@local"
              git config --global user.name "$(Build.QueuedBy)"              
              git checkout -b $(TARGET_DEFAULT_BRANCH)
              echo "This repository created from https://dev.azure.com/$(SOURCE_ORG)/$(SOURCE_PROJECT)/_git/$(name)/ by TF Mirror" > README.md
              git add README.md
              git commit -m "initalize git repository"
              git push origin refs/heads/$(TARGET_DEFAULT_BRANCH)
              cd ../../src/$(name)
            fi

            if [[ "${{ parameters.tfpatch }}" == "False" ]]
            then
              # Finally, push the repository using all the configs defined above, but still using the mirroring operation.
              git push --mirror
            else
              # Incremental sync - patch selectively, push one-by-one
              export GIT_CONFIG_COUNT=1 # remove the push override config
              git config --global user.email "$(Build.QueuedById)@local"
              git config --global user.name "$(Build.QueuedBy)"
              # collect target tags to filter out from patching operation
              TARGET_TAGS=$(git ls-remote --tags https://$(TARGET_ORG):$(TARGET_PAT)@dev.azure.com/$REPOSITORY_URL_SUFFIX | grep -v "\^{}" | sed -E "s|([0-9a-f]){40}([ \t])+refs\/tags\/(.+)|\3|g")
              for tag in $(git tag)
              do
                  echo "$tag"
                  echo "--------------------------- Annotation -------------------------"
                  GIT_TAG_MESSAGE=$(git tag -l --format='%(contents)' $tag)
                  git tag -l --format='%(contents)' $tag
                  echo "----------------------------------------------------------------"
                  # check if the tag in question already exists in the target repo
                  TAG_FOUND="0"
                  for target_tag in $TARGET_TAGS
                  do
                    if [[ "$tag" == "$target_tag" ]]
                    then
                      TAG_FOUND="1"
                    fi
                  done
                  if [[ "$TAG_FOUND" == "0" ]]
                  then
                    echo "TAG: $tag"
                    echo "----------------------------------------------------------"                    
                    git checkout -b $(TARGET_BRANCH_PREFIX)/$tag tags/$tag
                    ORIG_IFS=$IFS
                    IFS=$'\n'
                    for tffiles in $(find . -type f -name '*.tf')
                    do
                        echo "$tffiles"
                        sed -i -E "s|(source([ \t])*=([ \t])*\"git::https:\/\/)$(SOURCE_ORG)(@dev.azure.com\/)$(SOURCE_ORG)\/$(SOURCE_PROJECT)\/_git\/(([_a-zA-Z0-9\-])*)(([\/_a-zA-Z0-9\-])*)(\?ref=([\/\\_a-zA-Z0-9\-\.])+\")|\1$(TARGET_ORG)\4$(TARGET_ORG)\/$(TARGET_PROJECT)\/_git\/$(TARGET_PREFIX)\5\7\9|g" "$tffiles"
                    done
                    IFS=$ORIG_IFS
                    echo "---------------------- DIFF ------------------------------"
                    git diff
                    echo "----------------------------------------------------------"
                    git diff --quiet || git commit -a -m "$tag - module sources regenerated"
                    git push origin refs/heads/$(TARGET_BRANCH_PREFIX)/$tag
                    git tag -f $tag -a -m "$GIT_TAG_MESSAGE"
                    git push origin refs/tags/$tag
                  fi
              done
            fi
            echo "Finished mirroring into $(TARGET_ORG)"
            echo "---------------------------------------------------------"
            echo ""            
        env:
          AZURE_DEVOPS_EXT_PAT: $(TARGET_PAT)
  - stage: Cleanup
    condition: eq('${{ parameters.cleanup }}','True')
    jobs:
    - job: CleanupJob
      steps:
      - task: Bash@3
        name: DeleteRepos
        inputs:
          targetType: 'inline'
          script: |
            for id in $(az repos list --org https://dev.azure.com/$(TARGET_ORG)/ -p $(TARGET_PROJECT) | jq -r '.[] | select(.name? | match("$(TARGET_PREFIX).")) | select(.name? | match("$(SOURCE_FILTER_REGEX)")) | .id')
            do
              az repos delete --id $id --org https://dev.azure.com/$(TARGET_ORG)/ --project $(TARGET_PROJECT) --yes
            done
        env:
          AZURE_DEVOPS_EXT_PAT: $(TARGET_PAT)