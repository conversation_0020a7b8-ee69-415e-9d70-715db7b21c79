trigger: none

schedules:
- cron: 0 5 * * *
  displayName: Daily Build
  branches:
    include: [ main ]
  always: true

resources:
  repositories:
    - repository: tooling
      type: git
      name: tooling

variables:
  - group: release-page

stages:
- stage: ReleasePage
  jobs:
  - job: BuildReleasePageJob
    pool: DEV-AksPool-centralagent-Deploy
    steps:
    - checkout: self
    - task: Bash@3
      name: DockerBuildCollect
      displayName: Git Data Collect
      inputs:
        targetType: inline
        script: |
          if [[ -f "~/.gitconfig" ]]; then
            rm ~/.gitconfig
          fi        
          git config --global url."https://ADOS-OTPHU-01:$(RP_PAT)@dev.azure.com".insteadOf https://<EMAIL>
          ORG_NAME=$(echo "$(System.CollectionUri)" | cut -c 23-)
          export AZURE_DEVOPS_EXT_PAT="$(RP_PAT)"
          az config set extension.use_dynamic_install=yes_without_prompt
          az repos list --org $(System.CollectionUri) -p $(System.TeamProject) >> repos.json
          cat $(Build.SourcesDirectory)/pipelines/release-head2.html > index2.html
          for REPO_NAME in $(cat repos.json | jq -r '.[] | select(.name | startswith("terraform-")) | .name')
          do
            REPO_URL=$(cat repos.json | jq -r ".[] | select(.name == \"${REPO_NAME}\") | .remoteUrl")
            REPO_ID=$(cat repos.json | jq -r ".[] | select(.name == \"${REPO_NAME}\") | .id")
            REPO_TAGS_JSON=$(az repos ref list --org $(System.CollectionUri) -p $(System.TeamProject) -r ${REPO_NAME} --filter "tags/")
            REPO_TAGS=$(echo "${REPO_TAGS_JSON}" | jq -r '.[].name')
            rm -f CHANGELOG.md
            if [[ "$REPO_TAGS" != "" ]]
            then
              git clone -q ${REPO_URL}
              if [ -f "./${REPO_NAME}/CHANGELOG.md" ]
              then
                echo "repo: ${REPO_NAME}"
                cp ./${REPO_NAME}/CHANGELOG.md ./
                for REPO_TAG in ${REPO_TAGS}
                do
                  CHANGELOG_IN_CURRENT_VER="0"
                  TAG_HAS_CHANGELOG="0"
                  TAG_DEPRECATED="0"
                  TAG_CURRENT="0"
                  RELEASE_NOTE=""
                  REPO_VER=$(echo "${REPO_TAG}" | cut -c 11-)
                  REPO_TAG_JSON=$(echo "$REPO_TAGS_JSON" | jq -r ".[] | select(.name == \"${REPO_TAG}\")")
                  OWNER=$(echo "$REPO_TAG_JSON" | jq -r ".creator.displayName")
                  OWNER_UPN=$(echo "$REPO_TAG_JSON" | jq -r ".creator.uniqueName")
                  OBJECT_ID=$(echo "$REPO_TAG_JSON" | jq -r ".objectId")
                  ANNOTATION_JSON=$(curl -s "https://ADOS-OTPHU-01:$(RP_PAT)@dev.azure.com/${ORG_NAME}$(System.TeamProject)/_apis/git/repositories/${REPO_ID}/annotatedtags/${OBJECT_ID}?api-version=6.0-preview.1")
                  TAG_DATE=$(echo "$ANNOTATION_JSON" | jq -r ".taggedBy.date")
                  echo "${REPO_VER} - ${OWNER} (${OWNER_UPN}) - ${TAG_DATE}"
                  SOURCE_URL="$(System.CollectionUri)$(System.TeamProject)/_git/${REPO_NAME}?path=%2F&version=GT${REPO_VER}&_a=contents"
                  echo "SOURCE URL: ${SOURCE_URL}"
                  OLDIFS=$IFS
                  IFS=$'\n'
                  for CHANGELOG_LINE in $(cat ./CHANGELOG.md)
                  do
                    if [[ "$(echo "${CHANGELOG_LINE}" | cut -c 1-1)" == "#" ]]
                    then
                      CHANGELOG_IN_CURRENT_VER="0"
                    fi
                    if [[ "$(echo "${CHANGELOG_LINE}" | cut -c 1-3)" == "###" ]]
                    then
                      if [[ "$(echo "${CHANGELOG_LINE}" | grep -c ${REPO_VER})" == "1" ]]
                      then
                        CHANGELOG_IN_CURRENT_VER="1"
                        TAG_HAS_CHANGELOG="1"
                        TAG_DEPRECATED=$(echo "${CHANGELOG_LINE}" | grep -c "\[deprecated\]")
                        TAG_CURRENT=$(echo "${CHANGELOG_LINE}" | grep -c "\[current\]")
                      fi
                    else
                      if [[ "$CHANGELOG_IN_CURRENT_VER" == "1" ]]
                      then
                        echo "${CHANGELOG_LINE}"
                        RELEASE_NOTE="${RELEASE_NOTE}
          ${CHANGELOG_LINE}"
                      fi
                    fi
                  done
                  IFS=$OLDIFS
                  if [[ "${TAG_HAS_CHANGELOG}" == "1" ]]
                  then
                    TR_OPTION=""
                    #if [[ "$TAG_DEPRECATED" == "1" ]]
                    #then
                    #  TR_OPTION="bgcolor=\"Orange\""
                    #fi
                    if [[ "$TAG_CURRENT" == "1" ]]
                    then
                    #  TR_OPTION="bgcolor=\"MediumSeaGreen\""
                    #fi
                    echo "<tr ${TR_OPTION}><td>${REPO_NAME}</td><td>${REPO_VER}</td><td>${TAG_DATE}<br/>${OWNER} (${OWNER_UPN})<br/><a href="${SOURCE_URL}" target="_blank">Source Code</a></td><td><pre>${RELEASE_NOTE}</pre></td></tr>" >> index2.html
                    fi
                  fi
                done
              fi
            fi
          done
          cat $(Build.SourcesDirectory)/pipelines/release-foot.html >> index2.html
    - task: PublishBuildArtifacts@1
      inputs:
        pathToPublish: 'index2.html'
        artifactName: web-page
  - job: PublishReleasePageJob
    pool: DEV-AksPool-centralagent-Deploy
    dependsOn:
      - BuildReleasePageJob
    steps:
    - task: DownloadBuildArtifacts@0
      inputs:
        buildType: 'current'
        downloadType: 'single'
        artifactName: web-page
    - task: AzureCLI@2
      displayName: 'Publish Page'
      name: PublishPage
      inputs:
        azureSubscription: 'DEV-OTP-DD-COEINFDEV-sub-dev-01'
        scriptType: 'bash'
        scriptLocation: 'inlineScript'
        inlineScript: |
          az storage blob upload --auth-mode login --overwrite True --file $(Build.ArtifactStagingDirectory)/web-page/index2.html --account-name $(STORAGE_ACCOUNT) --container-name "\$web" --name index2.html


