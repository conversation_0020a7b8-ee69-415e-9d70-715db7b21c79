parameters:
- name: folder
  type: string
- name: tf_command
  type: string
- name: timeout_in_minutes
  type: number
  default: 60
- name: no_proxy
  type: string
  default: '.core.windows.net'
- name: tf_debug
  type: string
  default: ''

jobs:
- job: terrafomExec
  pool: DEV-AksPool-centralagent-Deploy
  timeoutInMinutes: ${{ parameters.timeout_in_minutes }}
  variables:
    - group: pr-validate
  workspace:
    clean: all

  steps:
  - checkout: self
    clean: true
    persistCredentials: true
  - bash: |
      set -e
      ls -la ~/
      if [[ -f "~/.gitconfig" ]]; then
        rm ~/.gitconfig
      fi
      git config --global http.https://dev.azure.com.proxy ":@10.42.25.62:8083"
      git config --global url."https://ADOS-OTPHU-01:$(GZ_PAT)@dev.azure.com".insteadOf https://<EMAIL>
      echo "===== git config ====="
      cat ~/.gitconfig
      TF_COMMAND="${{ parameters.tf_command }}"
      if [ -d ./local_provider ]
      then
        echo "copy local provider"
        cp -r ./local_provider/* ~/
        # envsubst is required based on this: https://github.com/hashicorp/terraform/issues/27446
        cat ./local_provider/.terraformrc | envsubst > ~/.terraformrc
        cat ~/.terraformrc
      fi
      ls -la ~/
      cd ${{ parameters.folder }}
      echo "Running: terraform $TF_COMMAND"
      if [[ "$TF_COMMAND" == "apply" || "$TF_COMMAND" == "destroy" ]]
      then
        TF_COMMAND="$TF_COMMAND -auto-approve"
      fi
      unset NO_PROXY
      unset no_proxy
      unset NOPROXY
      unset noproxy
      export NO_PROXY=$CONCAT_PROXY
      
      echo ------------------------------------------------------------------------------------------
      echo Exported Environment variables
      echo ------------------------------------------------------------------------------------------
      for var in $(compgen -e); do
        echo $var ${!var};
      done
      echo ------------------------------------------------------------------------------------------
      echo NO_PROXY: $NO_PROXY
      echo PARAM_NO_PROXY: ${{ parameters.no_proxy }}
      echo ------------------------------------------------------------------------------------------
      echo "Build.QueuedBy: $(Build.QueuedBy)"
      echo "Build.QueuedById: $(Build.QueuedById)"
      export TF_VAR_owner="$(Build.QueuedBy)"
      if [[ "${{ parameters.tf_debug }}" != "none" ]]
      then
        export TF_LOG="${{ parameters.tf_debug }}"
      fi
      terraform init -upgrade
      terraform $TF_COMMAND
      if [[ -f "~/.terraformrc" ]]; then
        rm ~/.terraformrc
      fi      
    env:
      ARM_CLIENT_SECRET: $(ARM_CLIENT_SECRET)
      SBB_PAT: $(SBB_PAT)
      ${{ if and(gt(length(parameters.no_proxy), 0),ne(parameters.no_proxy,' ')) }}:
        CONCAT_PROXY: $(NO_PROXY),${{ parameters.no_proxy }}
      ${{ else }}:
        CONCAT_PROXY: $(NO_PROXY)
    displayName: "Terraform execute"
# - job: checkovJob
#   variables:
#     - group: pr-validate
#   workspace:
#     clean: all
#   pool:
#     vmImage: ubuntu-latest
#   steps:
#   - checkout: self
#   - bash: |
#       if [[ -f "~/.gitconfig" ]]; then
#         rm ~/.gitconfig
#       fi
#       git config --global url."https://ADOS-OTPHU-01:$(GZ_PAT)@dev.azure.com".insteadOf https://<EMAIL>
#       echo "===== git config ====="
#       cat ~/.gitconfig
#       cd ${{ parameters.folder }}
#       set -e
#       pip install checkov
#       export GITHUB_PAT="$(GZ_PAT)"
#       checkov -d . -o cli -o junitxml --output-file-path console,checkov-results.xml -s --download-external-modules true
#     displayName: "Checkov scan"
  - task: PublishTestResults@2
    inputs:
      testResultsFormat: 'JUnit'
      testResultsFiles: '**/checkov-results.xml'