jobs:
- job: checkovJob
  pool:
    name: DEV-AksPool-centralagent-Deploy
  variables:
    - group: pr-validate    
  steps:
  - checkout: self
  - bash: |
      if [[ -f "~/.gitconfig" ]]; then
        rm ~/.gitconfig
      fi
      git config --global url."https://ADOS-OTPHU-01:$(GZ_PAT)@dev.azure.com".insteadOf https://<EMAIL>
      echo "===== git config ====="
      cat ~/.gitconfig
      set -e
      pip install checkov
      export GITHUB_PAT="$(GZ_PAT)"
      for mod in `find . -name anchor.module -exec dirname {} \;`
      do
        INIT_DIR=`pwd`
        cd $mod/examples
        for dir in $(ls -d */)
        do
          MODULE_DIR=`pwd`
          dir=`echo $dir | tr -d '/'`
          cd $dir
          checkov -d . -o cli -o junitxml --output-file-path console,checkov-results_${dir}.xml -s --download-external-modules true
          sed -i 's/terraform scan/Checkov scan - '"$dir"'/' checkov-results_$dir.xml
          cat checkov-results_$dir.xml
          cd ${MODULE_DIR}
        done
        cd ${INIT_DIR}
      done
    displayName: "Checkovscan"
  - task: PublishTestResults@2
    inputs:
      testResultsFormat: 'JUnit'
      testResultsFiles: '**/checkov-results_*.xml'
      mergeTestResults: false
      publishRunAttachement: true