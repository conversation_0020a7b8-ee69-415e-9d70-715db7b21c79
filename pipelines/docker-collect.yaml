parameters:
- name: registry
  type: string
- name: repository
  type: string
- name: tag
  type: string

trigger: none
pool:
  vmImage: ubuntu-latest

variables:
  - group: docker_hub

stages:
- stage: DockerCollectStage
  jobs:
  - job: DockerCollectJob
    steps:
    - task: Docker@2
      name: DockerLogin
      displayName: Docker Login
      inputs:
        command: login
        containerRegistry: DockerHub-otpbankhu
    - task: Bash@3
      name: DockerTransfer
      displayName: Doecker Transfer
      inputs:
        targetType: inline
        script: |
          npm install snyk -g
          snyk auth $(snyk_key)
          source_image_full="${{ parameters.registry }}/${{ parameters.repository }}:${{ parameters.tag }}"
          docker pull ${source_image_full}
          snyk container test ${source_image_full}
          source_image=${{ parameters.repository }}
          target_repository="${source_image//\//_}"
          target_image_full="$(target_registry)/$(target_folder)/${target_repository}:${{ parameters.tag }}"
          docker tag ${source_image_full} ${target_image_full}
          docker -D push ${target_image_full}