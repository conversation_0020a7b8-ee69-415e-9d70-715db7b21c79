parameters:
  - name: target
    type: string
    default: OTP-DD-COEINFDEV-sub-dev-01

  - name: test
    type: string
    default: 01-default

  - name: destroy
    type: boolean
    default: true

  - name: use_custom_agent_pool
    type: boolean
    default: true

  - name: custom_agent_pool_name
    type: string
    default: 'DEV-AksPool-centralagent-Deploy'

  - name: no_proxy
    type: string
    default: ' '

  - name: goproxy
    type: string
    default: https://az-nexus.otpbank.hu/repository/anonymous-proxy-go-proxy.golang.org/,direct

  - name: gosumdb
    type: string
    default: off

  - name: gonosumdb
    type: string
    default: dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/

  - name: test_dir
    type: string
    default: terratest
  
  - name: iac_dir
    type: string
    default: ' '

  - name: terraformrc_dir
    type: string
    default: network-mirror

  - name: local_provider_dir
    type: string
    default: local_provider

  - name: timeout_in_minutes
    type: number
    default: 30

jobs:
  - job: Terratest
    pool:
      ${{ if eq(parameters.use_custom_agent_pool, true) }}:
        name: ${{ parameters.custom_agent_pool_name }}
      ${{ else }}:
        vmImage: 'ubuntu-latest'
    timeoutInMinutes: ${{ parameters.timeout_in_minutes }}
    variables:
    - name: goproxy
      value: ${{ parameters.goproxy }}
      # value: https://otpnexus.hu/repository/anonymous-proxy-go-proxy.golang.org/,direct
      # value: https://otptesztnexus.hu/repository/anonymous-proxy-go-proxy.golang.org/,direct
      # value: https://az-nexus.otpbank.hu/repository/anonymous-proxy-go-proxy.golang.org/,direct

    - name: gosumdb
      value: ${{ parameters.gosumdb }}

    - name: gonosumdb
      value: ${{ parameters.gonosumdb }}

    - name: iac_destroy
      value: ${{ parameters.destroy }}
    
    - name: iac_environment
      ${{ if eq(parameters.target, 'OTP-DD-COEINFDEV-sub-dev-01') }}:
        value: 'DEV'
      ${{ if eq(parameters.target, 'OTP-ADO-demo01-sub-dev-01') }}:
        value: 'DEV'
      ${{ if eq(parameters.target, 'OTP-ADO-IaC-sub-tst-01') }}:
        value: 'TST'
      ${{ if eq(parameters.target, 'OTP-ADO-IaC-sub-prd-01') }}:
        value: 'PRD'

    - name: iac_appcode
      ${{ if eq(parameters.target, 'OTP-DD-COEINFDEV-sub-dev-01') }}:
        value: 'terratest'
      ${{ if eq(parameters.target, 'OTP-ADO-demo01-sub-dev-01') }}:
        value: 'demo'
      ${{ if eq(parameters.target, 'OTP-ADO-IaC-sub-tst-01') }}:
        value: 'terratest'
      ${{ if eq(parameters.target, 'OTP-ADO-IaC-sub-prd-01') }}:
        value: 'terratest'

    - name: iac_service_connection
      ${{ if eq(parameters.target, 'OTP-DD-COEINFDEV-sub-dev-01') }}:
        value: 'DEV-OTP-DD-COEINFDEV-sub-dev-01'
      ${{ if eq(parameters.target, 'OTP-ADO-demo01-sub-dev-01') }}:
        value: 'OTP-ADO-demo01-sub-dev-01-sp-01'
      ${{ if eq(parameters.target, 'OTP-ADO-IaC-sub-tst-01') }}:
        value: 'TST-OTP-ADO-IaC-sub-tst-01-OTPHU-COE-TEMPLATESPEC'
      ${{ if eq(parameters.target, 'OTP-ADO-IaC-sub-prd-01') }}:
        value: 'PRD-OTP-ADO-IaC-sub-prd-01-OTPHU-COE-TEMPLATESPEC'

    - name: iac_storage_rgrp
      ${{ if eq(parameters.target, 'OTP-DD-COEINFDEV-sub-dev-01') }}:
        value: 'OTP-DD-COEINFDEV-sub-dev-01-rg-westeu-01'
      ${{ if eq(parameters.target, 'OTP-ADO-demo01-sub-dev-01') }}:
        value: 'rgrp-weu-dev-demo01-01'
      ${{ if eq(parameters.target, 'OTP-ADO-IaC-sub-tst-01') }}:
        value: 'rgrp-weu-tst-iac-01'
      ${{ if eq(parameters.target, 'OTP-ADO-IaC-sub-prd-01') }}:
        value: 'rgrp-weu-prd-iac-01'

    - name: iac_storage_name
      ${{ if eq(parameters.target, 'OTP-DD-COEINFDEV-sub-dev-01') }}:
        value: 'coeinfdevtfstate01'
      ${{ if eq(parameters.target, 'OTP-ADO-demo01-sub-dev-01') }}:
        value: 'stacweudevdevd000001'
      ${{ if eq(parameters.target, 'OTP-ADO-IaC-sub-tst-01') }}:
        value: 'stacweutstritm1669979'
      ${{ if eq(parameters.target, 'OTP-ADO-IaC-sub-prd-01') }}:
        value: 'stacweuprdritm1670198'

    - name: iac_storage_container
      ${{ if eq(parameters.target, 'OTP-DD-COEINFDEV-sub-dev-01') }}:
        value: 'tfstate-terratest'
      ${{ if eq(parameters.target, 'OTP-ADO-demo01-sub-dev-01') }}:
        value: 'tfstate'
      ${{ if eq(parameters.target, 'OTP-ADO-IaC-sub-tst-01') }}:
        value: 'tfstate-terratest'
      ${{ if eq(parameters.target, 'OTP-ADO-IaC-sub-prd-01') }}:
        value: 'tfstate-terratest'

    - name: iac_storage_key
      value: ${{ format('{0}.{1}.tfstate', variables['Build.Repository.Name'], parameters.test) }}
    - group: sbb-pat

    - group: 'Centrally managed variable group'

    # environment: ${{ variables.iac_environment }}-${{ variables.iac_appcode }}

    workspace:
      clean: all
    steps:
      - checkout: self
        clean: true
        persistCredentials: true

      # Set Variables
      
      - task: AzureCLI@2
        name: setVariables
        displayName: Set Variables
        continueOnError: false
        inputs:
          azureSubscription: ${{ variables.iac_service_connection }}
          addSpnToEnvironment: true
          scriptType: 'bash'
          scriptLocation: 'inlineScript'
          inlineScript: |
            echo "Exporting environment variables for Terratest"
            echo "##vso[task.setvariable variable=iac_target]${{ parameters.target }}"
            echo "##vso[task.setvariable variable=iac_test]${{ parameters.test }}"
            echo "##vso[task.setvariable variable=iac_test_dir]${{ parameters.test_dir }}"
            echo "##vso[task.setvariable variable=iac_dir]${{ parameters.iac_dir }}"
            if [[ -z "${{ parameters.iac_dir }}" || "${{ parameters.iac_dir }}" == " " ]]; then
              echo "The iac_dir parameter is empty."
            else
              IAC_SUBMODULE=$(echo '${{ parameters.iac_dir }}' | cut -d'/' -f1)
              echo "Extracted submodule: $IAC_SUBMODULE"
              echo "##vso[task.setvariable variable=iac_submodule]$IAC_SUBMODULE"
            fi

      - bash: env
        displayName: Run 'env'

      - task: Go@0
        inputs:
          command: "version"
        displayName: Run 'go version'

      - task: Go@0
        inputs:
          command: "env"
        displayName: Run 'go env'
      
      # Checking if Go module is initialized. If not, make up for it by running 'go init', 'git commit', 'git push' commands

      - bash: |
          GOMODPRESENCE=$(ls -la ${{ parameters.test_dir }} | grep go.mod)
          if [ "$GOMODPRESENCE" = "" ]; then
            echo "##vso[task.setvariable variable=GoModuleState]not_initiated"
          fi
        displayName: Checking if 'go.mod' file is present

      - bash: |
          cd ${{ parameters.test_dir }}
          git config --global url."https://ADOS-OTPHU-01:$(SBB_PAT)@dev.azure.com".insteadOf https://dev.azure.com
          go mod init 'SBB/$(Build.Repository.Name)/${{ parameters.test }}'
          go mod tidy
        displayName: Run 'go mod init'
        condition: eq(variables.GoModuleState, 'not_initiated')
        env:
          GOPROXY: ${{ variables.goproxy }}
          GOSUMDB: ${{ variables.gosumdb }}
          GONOSUMDB: ${{ variables.gonosumdb }}
          SBB_PAT: $(SBB_PAT)

      - bash: |
          echo "Configure git user.name, user.email"
          git config --global user.email "<EMAIL>"
          git config --global user.name "CI Agent"
          
          if [ -n "$SYSTEM_PULLREQUEST_SOURCEBRANCH" ]
          then
            branch=`echo $SYSTEM_PULLREQUEST_SOURCEBRANCH | cut -d/ -f 3-`
            echo "This is a PR. Branch name: "$branch
          else
            branch=`echo $BUILD_SOURCEBRANCH | cut -d/ -f 3-`        
            echo "This is not a PR. Branch name: "$branch
          fi

          echo "Switch to target branch"
          git switch -c $branch

          echo "Show branches"
          git branch

          echo "Stage git changes"
          git add -A

          echo "Commit git changes"
          git commit -m "***NO_CI***"

          echo "Push git changes"
          git push -f origin HEAD
        displayName: Run 'git commit' & 'push'
        condition: eq(variables.GoModuleState, 'not_initiated')

       # Download Go modules (dependencies)

      - bash: |
          git config --global url."https://ADOS-OTPHU-01:$(SBB_PAT)@dev.azure.com".insteadOf https://dev.azure.com
          export NO_PROXY="$NO_PROXY,$no_proxy"
          echo "NO_PROXY: $NO_PROXY"
          cd ${{ parameters.test_dir }} && go mod download -x
        displayName: Run 'go mod download'
        env:
          GOPROXY: ${{ variables.goproxy }}
          GOSUMDB: ${{ variables.gosumdb }}
          GONOSUMDB: ${{ variables.gonosumdb }}
          SBB_PAT: $(SBB_PAT)

      # Install tools

      - bash: |
          export NO_PROXY="$NO_PROXY,$no_proxy"
          echo "NO_PROXY: $NO_PROXY"
          export GOSUMDB=off
          go mod download -x github.com/jstemmer/go-junit-report@v1.0.0
          go install github.com/jstemmer/go-junit-report@v1.0.0
        env:
          GOPROXY: ${{ variables.goproxy }}
        displayName: Install 'go-junit-report' Tool

      - bash: |
          go_path_string=$(go env | grep GOPATH)
          IFS='"' read -ra go_path_arr <<< $go_path_string
          go_bin_path=${go_path_arr[1]}/bin
          echo $go_bin_path
          echo "##vso[task.setvariable variable=GoBin]$go_bin_path"
        displayName: Set GoBin variable value

      # Copy .terraformrc
      
      - bash: |
          if [ -f "$(System.DefaultWorkingDirectory)/${{ parameters.terraformrc_dir }}/.terraformrc" ]
          then
            echo "Copying .terraformrc to home directory"
            cat "$(System.DefaultWorkingDirectory)/${{ parameters.terraformrc_dir }}/.terraformrc" | envsubst > ~/.terraformrc
            cat ~/.terraformrc
          else
            echo ".terraformrc file not found in $(System.DefaultWorkingDirectory)/${{ parameters.terraformrc_dir }}"
          fi
        displayName: 'Copy .terraformrc to user home'

      # Copy local providers
      
      - bash: |
          echo "Checking for local providers in ${{ parameters.local_provider_dir }}"
          if [ -d ${{ parameters.local_provider_dir }} ]
          then
            echo "Copying local providers to home directory"
            cp -r $(System.DefaultWorkingDirectory)/${{ parameters.local_provider_dir }}/* ~/
          else
            echo "Local providers directory not found: ${{ parameters.local_provider_dir }}"
          fi
        displayName: 'Copy local providers to user home'

      # Running Terratest

      - task: AzureCLI@2
        inputs:
          azureSubscription: ${{ variables.iac_service_connection }}
          addSpnToEnvironment: true
          scriptType: 'bash'
          scriptLocation: 'inlineScript'
          inlineScript: |
            set -e
            ls -la ~/
            if [[ -f "~/.gitconfig" ]]; then
              rm ~/.gitconfig
            fi
            git config --global http.https://dev.azure.com.proxy ":@***********:8083"
            git config --global url."https://ADOS-OTPHU-01:$(SBB_PAT)@dev.azure.com".insteadOf https://<EMAIL>
            echo "===== git config ====="
            cat ~/.gitconfig
            # try local proxy
            unset NO_PROXY
            unset no_proxy
            unset NOPROXY
            unset noproxy
            export NO_PROXY=$CONCAT_PROXY
            echo ------------------------------------------------------------------------------------------
            echo Exported Environment variables
            echo ------------------------------------------------------------------------------------------
            for var in $(compgen -e); do
              echo $var ${!var};
            done
            echo ------------------------------------------------------------------------------------------
            echo NO_PROXY: $NO_PROXY
            echo PARAM_NO_PROXY: ${{ parameters.no_proxy }}
            echo HTTP_PROXY: $HTTP_PROXY
            echo HTTPS_PROXY: $HTTPS_PROXY
            echo ------------------------------------------------------------------------------------------
            export ARM_CLIENT_ID=$servicePrincipalId
            export ARM_CLIENT_SECRET=$servicePrincipalKey
            export ARM_SUBSCRIPTION_ID=`az account show -o json | jq .id | tr -d '"'`
            export ARM_TENANT_ID=$tenantId
            export TF_VAR_owner="$(Build.QueuedBy)"
            # run terratest
            cd $(System.DefaultWorkingDirectory)/${{ parameters.test_dir }}
            env | grep '^PWD='
            go test -v -timeout ${{ parameters.timeout_in_minutes }}m 2>&1 $@ | tee test-logs.txt
            TT_EXITCODE=${PIPESTATUS[0]}
            echo " 🔽 ### ### ### ### ### ###    Test Summary    ### ### ### ### ### ### 🔽 "
            cat test-logs.txt | grep "\--- "
            echo " 🔼 ### ### ### ### ### ### ### ########## ### ### ### ### ### ### ### 🔼 "
            if [ $TT_EXITCODE -ne 0 ]; then exit 1; fi
            echo "Trying to delete .terraformrc"
            if [[ -f "~/.terraformrc" ]]; then
              rm ~/.terraformrc
            fi
            # Fetch the resource group name from Terraform output, assuming your Terratest code outputs it
            # Make sure this is done after Terraform apply, within the Go test
            echo "Fetching the resource group name from Terraform output"
            echo "##vso[task.setvariable variable=IAC_RESOURCE_GROUP_NAME;isOutput=true]$RESOURCE_GROUP_NAME"
        env:
          SBB_PAT: $(SBB_PAT)
          ${{ if and(gt(length(parameters.no_proxy), 0),ne(parameters.no_proxy,' ')) }}:
            CONCAT_PROXY: $(NO_PROXY),${{ parameters.no_proxy }}
          ${{ else }}:
            CONCAT_PROXY: $(NO_PROXY)
        displayName: 'Run Terratest'

      # - bash: env
      #   displayName: Run 'env' 2

      # Cleanup
      - task: AzureCLI@2
        displayName: 'Cleanup'
        condition: and(eq(variables['iac_destroy'], true), failed()) 
        inputs:
          azureSubscription: ${{ variables.iac_service_connection }}
          scriptType: 'bash'
          scriptLocation: 'inlineScript'
          inlineScript: |
            # Use the environment variable IAC_RESOURCE_GROUP_NAME that was set in the previous task
            az group delete --resource-group $(IAC_RESOURCE_GROUP_NAME) --yes --no-wait
        # The above command forcefully deletes the specified resource group without prompting for confirmation and doesn't wait for the operation to complete
        env:
          IAC_RESOURCE_GROUP_NAME: $(IAC_RESOURCE_GROUP_NAME)

      # JUnitXML Test report tasks:

      - bash: |
          cat $(System.DefaultWorkingDirectory)/${{ parameters.test_dir }}/test-logs.txt | $(GoBin)/go-junit-report > terratest-junit-report.xml
        displayName: Create JUnitXML test report
        condition: always()

      - task: PublishTestResults@2
        inputs:
          testResultsFormat: 'JUnit'
          testResultsFiles: '**/terratest-junit-report.xml'
        displayName: Publish JUnitXML test report
        condition: always()