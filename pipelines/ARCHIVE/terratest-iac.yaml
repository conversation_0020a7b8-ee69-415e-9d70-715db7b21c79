parameters:
  - name: timeout_in_minutes
    type: number
    default: 60
  - name: no_proxy
    type: string
    default: '*.core.windows.net'
  - name: azure_subscription
    type: string
  - name: subscription_id
    type: string
  - name: use_custom_agent_pool
    type: boolean
    default: false
  - name: custom_agent_pool_name
    type: string
  - name: test_folder
    type: string
    default: terratest

jobs:
  - job: Terratest
    pool:
      ${{ if parameters.use_custom_agent_pool }}:
        name: ${{ parameters.custom_agent_pool_name }}
      ${{ else }}:
        vmImage: 'ubuntu-latest'
    timeoutInMinutes: ${{ parameters.timeout_in_minutes }}
    variables:
      - name: goproxy
        value: https://otpnexus.hu/repository/anonymous-proxy-go-proxy.golang.org/,direct
      - group: sbb-pat
    steps:
      - checkout: self
        clean: true
        persistCredentials: true

      - bash: |
          env
        displayName: Run 'env'

      - task: Go@0
        inputs:
          command: "version"
        displayName: Run 'go version'

      - task: Go@0
        inputs:
          command: "env"
        displayName: Run 'go env'

      # Checking if Go module is initialized. If not, make up for it by running 'go init', 'git commit', 'git push' commands
      - bash: |
          GOMODPRESENCE=$(ls -la terratest | grep go.mod)
          if [ "$GOMODPRESENCE" = "" ]; then
            echo "##vso[task.setvariable variable=GoModuleState]not_initiated"
          fi
        displayName: Checking if 'go.mod' file is present

      - bash: |
          cd ${{ parameters.test_dir }}
          go mod init 'SBB/$(Build.Repository.Name)/${{ parameters.test }}'
          go mod tidy
        displayName: Run 'go mod init'
        condition: eq(variables.GoModuleState, 'not_initiated')

      # Azure CLI task to fetch service principal details from the service connection
      - task: AzureCLI@2
        inputs:
          azureSubscription: ${{ parameters.azure_subscription }}
          scriptType: 'bash'
          scriptLocation: 'inlineScript'
          inlineScript: 'az login --service-principal -u $(servicePrincipalClientId) -p $(servicePrincipalClientSecret) --tenant $(servicePrincipalTenant)'
          addSpnToEnvironment: true
        displayName: 'Azure CLI - Login'
      
      - bash: |
          echo "Configure git user.name, user.email"
          git config --global user.email "<EMAIL>"
          git config --global user.name "CI Agent"

          if [ -n "$SYSTEM_PULLREQUEST_SOURCEBRANCH" ]; then
            branch=`echo $SYSTEM_PULLREQUEST_SOURCEBRANCH | cut -d/ -f 3-`
            echo "This is a PR. Branch name: "$branch
          else
            branch=`echo $BUILD_SOURCEBRANCH | cut -d/ -f 3-`        
            echo "This is not a PR. Branch name: "$branch
          fi

          echo "Switch to target branch"
          git switch -c $branch

          echo "Show branches"
          git branch

          echo "Stage git changes"
          git add -A

          echo "Commit git changes"
          git commit -m "***NO_CI***"

          echo "Push git changes"
          git push -f origin HEAD
        displayName: Run 'git commit' & 'push'
        condition: eq(variables.GoModuleState, 'not_initiated')

      # Download Go modules (dependencies)
      - bash: |
          export NO_PROXY="$NO_PROXY,$no_proxy"
          echo "NO_PROXY: $NO_PROXY"
          export GOSUMDB=off 
          cd terratest && go mod download -x
        env:
          GOPROXY: ${{ variables.goproxy }}
        displayName: Run 'go mod download'

      # Install tools
      - bash: |
          export NO_PROXY="$NO_PROXY,$no_proxy"
          echo "NO_PROXY: $NO_PROXY"
          export GOSUMDB=off
          go mod download -x github.com/jstemmer/go-junit-report@v1.0.0
          go install github.com/jstemmer/go-junit-report@v1.0.0
        env:
          GOPROXY: ${{ variables.goproxy }}
        displayName: Install 'go-junit-report' Tool

      - bash: |
          go_path_string=$(go env | grep GOPATH)
          IFS='"' read -ra go_path_arr <<< $go_path_string
          go_bin_path=${go_path_arr[1]}/bin
          echo $go_bin_path
          echo "##vso[task.setvariable variable=GoBin]$go_bin_path"
        displayName: Set GoBin variable value

      - bash: |
          echo "Checking local provider folder"
          pwd
          ls -la
          echo "home is $HOME"
          if [ -d ./local_provider ]; then
            echo "copy local provider"
            cp -r ./local_provider/* ~/
            # envsubst is required based on this: https://github.com/hashicorp/terraform/issues/27446
            cat ./local_provider/.terraformrc | envsubst > ~/.terraformrc
            cat ~/.terraformrc
          fi
          ls -la ~/
        displayName: Check if local provider required

      # Running Terratest
      - bash: |
          set -e
          ls -la ~/
          if [[ -f "~/.gitconfig" ]]; then
            rm ~/.gitconfig
          fi
          git config --global http.https://dev.azure.com.proxy ":@10.42.25.62:8083"
          git config --global url."https://ADOS-OTPHU-01:$(SBB_PAT)@dev.azure.com".insteadOf https://<EMAIL>
          echo "===== git config ====="
          cat ~/.gitconfig
          # try local proxy
          unset NO_PROXY
          unset no_proxy
          unset NOPROXY
          unset noproxy
          export NO_PROXY=$CONCAT_PROXY
          #export HTTP_PROXY="http://127.0.0.1:8080"
          #export HTTPS_PROXY="http://127.0.0.1:8080" 
          echo ------------------------------------------------------------------------------------------
          echo Exported Environment variables
          echo ------------------------------------------------------------------------------------------
          for var in $(compgen -e); do
            echo $var ${!var};
          done
          echo ------------------------------------------------------------------------------------------
          echo NO_PROXY: $NO_PROXY
          echo PARAM_NO_PROXY: ${{ parameters.no_proxy }}
          echo HTTP_PROXY: $HTTP_PROXY
          echo HTTPS_PROXY: $HTTPS_PROXY
          echo ------------------------------------------------------------------------------------------         
          export TF_VAR_owner="$(Build.QueuedBy)"
          # run terratest
          cd $(System.DefaultWorkingDirectory)/${{ parameters.test_folder }}
          env | grep '^PWD='
          go test -v -timeout ${{ parameters.timeout_in_minutes }}m 2>&1 $@ | tee test-logs.txt
          TT_EXITCODE=${PIPESTATUS[0]}
          echo " 🔽 ### ### ### ### ### ###    Test Summary    ### ### ### ### ### ### 🔽 "
          cat test-logs.txt | grep "\--- "
          echo " 🔼 ### ### ### ### ### ### ### ########## ### ### ### ### ### ### ### 🔼 "
          if [ $TT_EXITCODE -ne 0 ]; then exit 1; fi
          echo "Trying to delete .terraformrc"
          if [[ -f "~/.terraformrc" ]]; then
            rm ~/.terraformrc
          fi    
        env:
          ARM_CLIENT_SECRET: $(servicePrincipalKey)
          ARM_CLIENT_ID: $(servicePrincipalClientId)
          ARM_TENANT_ID: $(servicePrincipalTenant)
          ARM_SUBSCRIPTION_ID: ${{ parameters.subscription_id }}
          ${{ if and(gt(length(parameters.no_proxy), 0),ne(parameters.no_proxy,' ')) }}:
            CONCAT_PROXY: $(NO_PROXY),${{ parameters.no_proxy }}
          ${{ else }}:
            CONCAT_PROXY: $(NO_PROXY)
        displayName: Run 'go test' (Terratest)

      # JUnitXML Test report tasks:
      - bash: |
          cat $(System.DefaultWorkingDirectory)/terratest/test-logs.txt | $(GoBin)/go-junit-report > terratest-junit-report.xml
        displayName: Create JUnitXML test report
        condition: always()

      - task: PublishTestResults@2
        inputs:
          testResultsFormat: 'JUnit'
          testResultsFiles: '**/terratest-junit-report.xml'
        displayName: Publish JUnitXML test report
        condition: always()
