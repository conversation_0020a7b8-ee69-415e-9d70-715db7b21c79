jobs:
- job: terrafomDocs
  pool: DEV-VmssPool-SBB-Deploy
  variables:
    - group: pr-validate
  workspace:
    clean: all

  steps:
  - checkout: self
    clean: true
    persistCredentials: true

  - bash: |
      #env
      echo "Configure git user.name, user.email"
      git config --global user.email "<EMAIL>"
      git config --global user.name "CI Agent"

      if [ -n "$SYSTEM_PULLREQUEST_SOURCEBRANCH" ]
      then
        branch=`echo $SYSTEM_PULLREQUEST_SOURCEBRANCH | cut -d/ -f 3-`
        echo "This is a PR. Branch name: "$branch
      else
        branch=`echo $BUILD_SOURCEBRANCH | cut -d/ -f 3-`        
        echo "This is not a PR. Branch name: "$branch
      fi

      echo "Switch to target branch"
      git switch -c $branch

      # Loop through all folders which have main.tf and generate USAGE.md
      for dir in `find . -name main.tf  -exec dirname {} \; | egrep -v "examples|tests"`
      do
        echo "In Directory: $dir"
        cd $dir
        echo "Format Terraform Code using terraform fmt"
        terraform fmt

        echo "Generate USAGE.md using terraform-docs"
        terraform-docs markdown . > USAGE.md

        cd -
      done

      # Rename changelog.md file to uppercase
      find . -iname changelog.md -type f -exec sh -c 'mv "$1" "${1%/*}/CHANGELOG.md"' _ {} \; 2> /dev/null
 

      echo "Stage git changes"
      git add -A

      echo "Commit git changes"
      git commit -m "***NO_CI***"

      echo "Push git changes"
      git push -f origin HEAD
    displayName: "Terraform Docs"

  - bash: |
      err=0
      # Do not check following directories
      exceptionlist="examples|tests|helm|src"
      
      # Check Confluence link in README.md
      grep "https://confluence.otpbank.hu" README.md 1>/dev/null
      if [ $? -ne 0 ]
      then
              echo "... Confluence documentation check: FAILED (Missing link in README.md to the OTP Confluence page)"
              err=1
      else
              echo "... Confluence documentation check: OK"
      fi

      # Check Confluence link on submodule level
      for i in $(ls -d */ | egrep -v $exceptionlist)
      do 
        if [ `grep -c "https://confluence.otpbank.hu" $i/README.md 2> /dev/null` -eq 0 ] 
        then 
              echo "... Confluence documentation check in $i directory: FAILED";err=1
        else
              echo "... Confluence documentation check in $i directory: OK"
        fi
      done

      # Check if CHANGELOG.md exists
      find . -iname ChangeLog.md | grep -i ChangeLog.md 1>/dev/null
      if [ $? -ne 0 ]
      then
              echo "... ChangeLog.md check: FAILED (Missing ChangeLog.md file)"
              err=1
      else
              echo "... ChangeLog.md check: OK"                          
      fi

      # Check if README.md exist in all submodules except tests directory
      for i in $(ls -d */ | egrep -v $exceptionlist)
      do 
        if [ `ls $i/README.md 2> /dev/null | grep -c README.md` -eq 0 ] 
        then 
              echo "... README.md check in $i directory: FAILED (README.md not found in $i directory)";err=1
        else
              echo "... README.md check in $i directory: OK"
        fi
      done

      # Check USAGE.md link in all submodules
      for i in $(ls -d */ | egrep -v $exceptionlist)
      do 
        if [ `grep -c USAGE.md $i/README.md 2> /dev/null` -eq 0 ] 
        then 
              echo "... USAGE.md link in README.md in $i directory: FAILED";err=1
        else
              echo "... USAGE.md link in README.md in $i directory: OK"
        fi
      done
      
      # Provider azurerm should not exist in modules only in examples and tests
      for i in $( ls -d */ )
      do 
        if [ -f "./$i/provider.tf" ] && [ `grep -c "provider \"azurerm\"" ./$i/provider.tf 2> /dev/null` -eq 1 ]
        then 
              echo "... Provider check in $i directory: FAILED (Provider azurerm should not exist in modules only in examples and tests)";err=1
        else
              echo "... Provider check in $i directory: OK"
        fi
      done

      if [ $err -eq 1 ]
      then
              exit 1
      fi
    displayName: "Semantic validation"
