parameters:
- name: registry
  type: string
  default: ' '
- name: repository
  type: string
- name: tag
  type: string
- name: dockerfile
  type: string
  default: ' '
- name: target
  type: string
  default: 'snapshot'
  values:
    - snapshot
    - release

trigger: none

resources:
  repositories:
    - repository: tooling
      type: git
      name: tooling
    - repository: docker
      type: git
      name: docker

variables:
  - group: docker_transfer

stages:
- stage: DockerCollectStage
  jobs:
  - job: DockerCollectJob
    pool:
      vmImage: ubuntu-latest
    steps:
    - checkout: self
    - checkout: docker
    - task: Bash@3
      name: DockerBuildCollect
      displayName: Docker Build/Collect
      inputs:
        targetType: inline
        script: |
          npm install -g snyk
          target_image_full="$(${{ parameters.target }}_target_registry)/${{ parameters.repository }}:${{ parameters.tag }}"
          if [[ "${{ parameters.registry }}" == " " ]]
          then
            # Build from Dockerfile
            buildah build -f $(Pipeline.Workspace)/s/docker/${{ parameters.dockerfile }} -t ${target_image_full}
          else
            # Download and tag
            buildah pull ${{ parameters.registry }}/${{ parameters.repository }}:${{ parameters.tag }}
            buildah tag ${{ parameters.registry }}/${{ parameters.repository }}:${{ parameters.tag }} ${target_image_full}
          fi
          buildah images
          # Create docker image archive
          buildah push --format docker ${target_image_full} docker-archive:image-artifact.tar
          SNYK_VERSION=$(snyk -v)
          snyk auth $(snyk_key)
          # Vulnerability scan
          snyk container test docker-archive:image-artifact.tar --sarif --severity-threshold=low > testresult.sarif
          # snyk container test docker-archive:image-artifact.tar --json > testresult.json
          # sarif-junit -i testresult.sarif -o testresult.xml --test-suite Snyk
          mkdir results
          echo "=== testresult.sarif ==="
          cat testresult.sarif
          echo "=== debug ==="
          TESTRESULT_JSON=$(cat testresult.sarif | jq -r '.runs[] | select(.tool.driver.rules | length > 0)')
          echo "TESTRESULT_JSON: $TESTRESULT_JSON"
          TEST_IDS=$(echo "${TESTRESULT_JSON}" | jq -r '.tool.driver.rules[].id')
          echo "TEST_IDS: $TEST_IDS"
          TEST_ERRORS=$(cat testresult.sarif | jq -r '[.runs[] | select(.tool.driver.rules | length > 0) | .results[] | select(.level!="note")] | length')
          echo "TEST_ERRORS: $TEST_ERRORS"
          TEST_NUM=$(cat testresult.sarif | jq -r '.runs[] | select(.tool.driver.rules | length > 0) | .results | length')
          echo "TEST_NUM: $TEST_NUM"
          echo "=== end debug ==="
          EMPTY_RESULT="0"
          if [[ "$TEST_NUM" == "" ]]
          then
            TEST_NUM="1"
            EMPTY_RESULT="1"
          fi
          echo "<?xml version=\"1.0\" encoding=\"UTF-8\"?><testsuites><testsuite name=\"Snyk (v${SNYK_VERSION})\" tests=\"${TEST_NUM}\" failures=\"${TEST_ERRORS}\" errors=\"0\" skipped=\"0\">" > ./testresult.xml
          if [[ "$EMPTY_RESULT" == "1" ]]
          then
            echo "<testcase classname=\"SUCCESS-TEST\" name=\"No vulnerabilities found\" file=\"N/A\" />" >> ./testresult.xml
          fi
          for TEST_ID in $TEST_IDS
          do
            # echo "ID: ${TEST_ID}"
            RULE=$(echo "${TESTRESULT_JSON}" | jq -r ".tool.driver.rules[] | select(.id==\"${TEST_ID}\")")
            RESULT=$(echo "${TESTRESULT_JSON}" | jq -r ".results[] | select(.ruleId==\"${TEST_ID}\")")
            rule_shortDescription=$(echo "${RULE}" | jq -r '.shortDescription.text')
            # echo "SHORTDESCRIPTION: ${rule_shortDescription}"
            rule_fullDescription=$(echo "${RULE}" | jq -r '.fullDescription.text')
            # echo "FULLDESCRIPTION: ${rule_fullDescription}"
            rule_markdown=$(echo "${RULE}" | jq -r '.help.markdown')
            result_level=$(echo "${RESULT}" | jq -r '.level')
            # echo "RESULTLEVEL: ${result_level}"
            result_message=$(echo "${RESULT}" | jq -r '.message.text')
            # echo "MESSAGE: ${result_message}"
            result_uris=$(echo "${RESULT}" | jq -r '.locations | map(.physicalLocation.artifactLocation.uri) | join("; ")')
            # echo "FILE: ${result_uris}\n"
            if [[ "$result_level" == "note" ]]
            then
              XML_CLOSE="/"
            else
              XML_CLOSE=""
            fi
            echo "<testcase classname=\"${TEST_ID}\" name=\"${rule_shortDescription}\" file=\"${result_uris}\" ${XML_CLOSE}>" >> ./testresult.xml
            if [[ "$result_level" != "note" ]]
            then
              echo "<failure type=\"failure\" message=\"${result_message}\">" >> ./testresult.xml
              echo "<![CDATA[" >> ./testresult.xml
              echo "Severity: ${result_level}" >> ./testresult.xml
              echo "Full description: ${rule_fullDescription}" >> ./testresult.xml
              echo "Snyk ID: ${TEST_ID}" >> ./testresult.xml
              echo "" >> ./testresult.xml
              echo "${rule_markdown}" >> ./testresult.xml
              echo "]]>" >> ./testresult.xml
              echo "</failure></testcase>" >> ./testresult.xml
            fi
          done
          echo "</testsuite></testsuites>" >> ./testresult.xml
          cp testresult.* ./results/
          cat ./results/testresult.xml

    - task: PublishBuildArtifacts@1
      inputs:
        pathToPublish: './results'
        artifactName: testresult-artifact
    - task: PublishTestResults@2
      inputs:
        testResultsFormat: 'JUnit'
        testResultsFiles: './testresult.xml'
        mergeTestResults: false
        publishRunAttachement: true
    - task: PublishBuildArtifacts@1
      inputs:
        pathToPublish: 'image-artifact.tar'
        artifactName: image-artifact          
  - job: DockerPublishJob
    pool: DEV-VmssPool-SBB-Deploy
    dependsOn:
      - DockerCollectJob
    steps:
    - task: DownloadBuildArtifacts@0
      inputs:
        buildType: 'current'
        downloadType: 'single'
        artifactName: image-artifact
    - task: Bash@3 
      name: DockerPublish
      displayName: Docker Publish
      inputs:
        targetType: inline
        script: |    
          target_image_full="$(${{ parameters.target }}_target_registry)/${{ parameters.repository }}:${{ parameters.tag }}"
          pwd
          ls -la $(Build.ArtifactStagingDirectory)/image-artifact/
          buildah login --tls-verify=false -u $(${{ parameters.target }}_target_user) -p $(${{ parameters.target }}_target_password) $(${{ parameters.target }}_target_registry)
          # Extract image
          buildah pull docker-archive:$(Build.ArtifactStagingDirectory)/image-artifact/image-artifact.tar
          buildah images
          # Push image to the Nexus
          buildah push ${target_image_full}

