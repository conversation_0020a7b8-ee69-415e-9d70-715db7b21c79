parameters:
- name: repositories
  type: object
  default:
  - terraform-azurerm-acr
  - terraform-azurerm-aks

trigger:
- none

resources:
  repositories:
    - repository: terraform-azurerm-acr
      type: git
      name: terraform-azurerm-acr
    - repository: terraform-azurerm-aks
      type: git
      name: terraform-azurerm-aks

steps:
- ${{ each value in parameters.repositories }}:
  - script: echo ${{ value }}