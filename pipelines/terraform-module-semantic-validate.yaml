trigger: none

jobs:
  - job: SemanticValidate
    displayName: "Semantic Validation"
    pool: DEV-AksPool-centralagent-Deploy
    variables:
      - group: pr-validate
    workspace:
      clean: all

    steps:
      - checkout: self
        clean: true
        persistCredentials: true

      - bash: |
          #env
          echo "Configure git user.name, user.email"
          git config --global user.email "<EMAIL>"
          git config --global user.name "CI Agent"

          if [ -n "$SYSTEM_PULLREQUEST_SOURCEBRANCH" ]
          then
            branch=`echo $SYSTEM_PULLREQUEST_SOURCEBRANCH | cut -d/ -f 3-`
            echo "This is a PR. Branch name: "$branch
          else
            branch=`echo $BUILD_SOURCEBRANCH | cut -d/ -f 3-`        
            echo "This is not a PR. Branch name: "$branch
          fi

          echo "Switch to target branch"
          git switch -c $branch
        displayName: "Git Checkout"

      - bash: |
            err=0
            RED='\033[0;31m'
            NC='\033[0m' # No Color

            # Define MODULES directories
            MODULES_DIR="$(find . -name anchor.module -exec dirname {} \;)"
            TEST_PIPELINES=$(grep -or --include "*.yaml" "default:.*examples.*" . | grep -oP " .*" | sort -u |  tr -d ' ' | tr -d '\r')

            # Function to check presence of file
            check_file_presence() {
                if [ ! -e "$1" ]; then
                    echo -e "  ${RED}FAILED${NC} - File not exists: $1"
                    err=1
                    return 1
                else
                    echo "  OK - File exists: $1"
                    return 0
                fi
            }

            # Function to check not presence of file
            check_file_not_presence() {
                if [ ! -e "$1" ]; then
                    echo "  OK - File not exists: $1"
                    return 0
                else
                    echo -e "  ${RED}FAILED${NC} - File exists: $1"
                    err=1
                    return 1
                fi
            }

            # Function to check the count of files
            check_file_count() {
                if [ $(ls -l $1 | wc -l) -ge $2 ]; then
                    echo "  OK - Number of '$1' files greater than or equal to $2"
                    return 0
                else
                    echo -e "  ${RED}FAILED${NC} - Number of '$1' files lower than $2"
                    err=1
                    return 1
                fi
            }

            # Function to check content in file
            check_file_content() {
                if grep -q "$2" "$1"; then
                    echo "  OK - Found '$2' in $1" | sed 's/\\//g'
                else
                    echo -e "  ${RED}FAILED${NC} - Did not find '$2' in $1" | sed 's/\\//g'
                    err=1
                fi
            }

            # Function to check content not in file
            check_file_not_content() {
                if grep -q "$2" "$1"; then
                    echo -e "  ${RED}FAILED${NC} - Found '$2' in $1"
                    err=1
                else
                    echo "  OK - Not found '$2' in $1"
                fi
            }

            # Function to check content count in file
            check_file_content_count() {
                if [ $(grep -c "$2" "$1") -eq $3 ]; then
                    echo "  OK - Found exactly $3 '$2' in $1" | sed 's/\\//g'
                else
                    echo -e "  ${RED}FAILED${NC} - Did not find exactly $3 '$2' in $1" | sed 's/\\//g'
                    err=1
                fi
            }

            check_azurerm_version() {
                azurerm_version="$(grep -ozP "azurerm\"\s+version.+(\d+\.\d+\.\d+)" $1 | grep -ozP "\d+\.\d+" | tr -d '\0')"
                major_modul=$(echo $azurerm_version | grep -oP "\d+\." | tr -d .)
                minor_modul=$(echo $azurerm_version | grep -oP "\.\d+" | tr -d .)
                major_min=$(echo $2 | grep -oP "\d+\." | tr -d .)
                minor_min=$(echo $2 | grep -oP "\.\d+" | tr -d .)
                if [ "$((major_modul))" -gt $(($major_min)) ] || ( [ "$((major_modul))" -eq $(($major_min)) ] && [ "$((minor_modul))" -ge $(($minor_min)) ] ); then
                    echo "  OK - azurerm provider version is $azurerm_version"
                else
                    echo -e "  ${RED}FAILED${NC} - azurerm provider version [$azurerm_version] lower than min: $2"
                    err=1
                fi
            }

            # Function to check if module version is configured properly in locals.tf
            check_module_verion() {
                if grep -q terraform- "$1"; then
                    VERSION_IN_CHANGELOG=`grep "\[current\]" $2 | cut -d' ' -f 2`
                    VERSION_IN_LOCALS=`grep terraform- $1 | cut -d'"' -f 2`

                    if [ ${VERSION_IN_CHANGELOG} == ${VERSION_IN_LOCALS} ]
                    then
                        echo "  OK - Version in locals.tf matches the version in CHANGELOG.md" 
                    else
                        echo -e "  ${RED}FAILED${NC} - Module version in locals.tf does not match the version in CHANGELOG.md: ${VERSION_IN_LOCALS} vs ${VERSION_IN_CHANGELOG}"
                        err=1
                    fi
                fi
            }

            # Check ROOT directory conditions
            echo "ROOT directory"
            check_file_presence ".gitignore"
            check_file_presence "./.azuredevops/pull_request_template/branches/main.MD"
            if check_file_presence "./README.md"; then
                check_file_content "./README.md" "https://confluence.otpbank.hu"
                check_file_not_content "./README.md" "USAGE.md"
            fi
            check_file_presence "./CHANGELOG.md" &&
                check_file_content_count "./CHANGELOG.md" "\[current\]" 1
            check_file_not_presence "azure-pipelines/tfdocs.yaml"
            check_file_count "azure-pipelines/*.yaml" 4

            for TEST_PIPELINE in ${TEST_PIPELINES}; do
                check_file_count "./$TEST_PIPELINE/*.tf" 6
            done        

            # Check MODULES directories conditions
            echo "MODULE directories"
            for MODULE in ${MODULES_DIR}; do
                if [ -d "$MODULE" ]; then
                    echo " MODULE: $(basename "$MODULE")"
                    if check_file_presence "$MODULE/locals.tf"; then
                        check_file_content "$MODULE/locals.tf" "tags"
                        check_module_verion "$MODULE/locals.tf" "CHANGELOG.md"
                    fi
                    if check_file_presence "$MODULE/README.md"; then
                        check_file_content "$MODULE/README.md" "https://confluence.otpbank.hu"
                        check_file_not_content "$MODULE/README.md" "USAGE.md"
                        check_file_not_content "$MODULE/README.md" "\`\`\`"
                        check_file_not_content "$MODULE/README.md" "## Description of sample code"
                    fi
                    check_file_presence "$MODULE/.config/.terraform-docs.yml"
                    MAIN_FILE="$(find $MODULE -name *main.tf | grep -v examples)"
                    if check_file_presence "$MAIN_FILE"; then
                        check_file_not_content "$MAIN_FILE" "locals {"
                    fi
                    check_file_not_presence "$MODULE/provider.tf"
                    if check_file_presence "$MODULE/terraform.tf"; then
                        check_file_not_content "$MODULE/terraform.tf" "required_version" &&
                            check_file_content "$MODULE/terraform.tf" "version.*>=" &&
                            check_azurerm_version "$MODULE/terraform.tf" "3.60"
                    fi
                fi
            done

            exit $err

        displayName: "Semantic validation"
