jobs:
- job: terrafomDocs
  pool: DEV-VmssPool-SBB-Deploy
  variables:
    - group: pr-validate
  workspace:
    clean: all

  steps:
  - checkout: self
    clean: true
    persistCredentials: true

  - bash: |
      #env
      echo "Configure git user.name, user.email"
      git config --global user.email "<EMAIL>"
      git config --global user.name "CI Agent"

      if [ -n "$SYSTEM_PULLREQUEST_SOURCEBRANCH" ]
      then
        branch=`echo $SYSTEM_PULLREQUEST_SOURCEBRANCH | cut -d/ -f 3-`
        echo "This is a PR. Branch name: "$branch
      else
        branch=`echo $BUILD_SOURCEBRANCH | cut -d/ -f 3-`        
        echo "This is not a PR. Branch name: "$branch
      fi

      echo "Switch to target branch"
      git switch -c $branch

      # Loop through all folders which have main.tf and generate USAGE.md
      for dir in `find . -name anchor.module -exec dirname {} \;`
      do
        echo "In Directory: $dir"
        cd $dir
        echo "Format Terraform Code using terraform fmt"
        terraform fmt

        echo "Generate USAGE.md using terraform-docs"
        terraform-docs markdown . > USAGE.md

        cd -
      done

      # Rename changelog.md file to uppercase
      find . -iname changelog.md -type f -exec sh -c 'mv "$1" "${1%/*}/CHANGELOG.md"' _ {} \; 2> /dev/null
 

      echo "Stage git changes"
      git add -A

      echo "Commit git changes"
      git commit -m "***NO_CI***"

      echo "Push git changes"
      git push -f origin HEAD
    displayName: "Terraform Docs"

