## yaml-language-server: $schema=https://raw.githubusercontent.com/microsoft/azure-pipelines-vscode/master/service-schema.json

jobs:
- job: gitversion
  pool: DEV-AksPool-centralagent-Deploy
  variables:
    CheckoutDirectory: '$(Agent.TempDirectory)/repo'
    CheckoutUrl: '${{ parameters.terratest_gitversion.resource.repository.remoteUrl }}'
    CommitMessage: '${{ parameters.terratest_gitversion.detailedMessage.text }}'
    GitAuth: '-c http.extraheader="AUTHORIZATION: bearer $(System.AccessToken)"'
    GitVersionConfig: '$(Agent.TempDirectory)/GitVersion.yml'
    CustomGitVersionConfig: '$(CheckoutDirectory)/.config/GitVersion.yml'
    FallbackGitVersionConfig: '$(Build.SourcesDirectory)/pipelines/GitVersion.yml'
    TeamsChannelHookUrl: ''
    ChangelogFile: $(Agent.TempDirectory)/changelog.md
  workspace:
    clean: all

  steps:
    - checkout: self

    - bash: |
        cat <<EOF
          ${{ convertToJson(parameters.terratest_gitversion) }}
        EOF
      displayName: Dump webhook trigger payload

    - bash: |
        source $(Build.SourcesDirectory)/pipelines/scripts/pr_tools.sh
        baseUrl="${{parameters.terratest_gitversion.resourceContainers.project.baseUrl}}"
        project="${{parameters.terratest_gitversion.resource.repository.project.name}}"
        pr_id=$(parse_pr_id "$(CommitMessage)")
        get_pr_description "$baseUrl" "$project" "$pr_id" "$(System.AccessToken)" > $(ChangelogFile)
        echo "PR Id: ${pr_id}"
        echo "PR Description:"
        cat $(ChangelogFile)
        echo "##vso[task.setvariable variable=pr_id;]${pr_id}"
      failOnStderr: "false"
      displayName: Fetch PR info

    - bash: |
        rm -rf $(CheckoutDirectory)
        mkdir -p $(CheckoutDirectory)
        git $(GitAuth) clone --progress --verbose $(CheckoutUrl) $(CheckoutDirectory)
      displayName: Checkout repository

    - bash: |
        if [ -e $(CustomGitVersionConfig) ]; then
          cp $(CustomGitVersionConfig) $(GitVersionConfig)
          echo "Using ${{ parameters.terratest_gitversion.resource.repository.name }} specific GitVersion config"
        else
          cp $(FallbackGitVersionConfig) $(GitVersionConfig)
          echo "Using common GitVersion config"
        fi
        cat $(GitVersionConfig)
      displayName: Configure GitVersion

    - task: gitversion/setup@0
      inputs:
        versionSpec: "5.x"

    - task: gitversion/execute@0
      inputs:
        targetPath: $(CheckoutDirectory)
        useConfigFile: true
        configFilePath: $(GitVersionConfig)
      displayName: Generate next version

    - bash: |
        echo "##vso[build.updatebuildnumber]${{ parameters.terratest_gitversion.resource.repository.name }}_$(GitVersion.SemVer)"
        set -e
        git config user.name "${{ parameters.terratest_gitversion.resource.pushedBy.displayName }}"
        git config user.email "${{ parameters.terratest_gitversion.resource.pushedBy.uniqueName }}"
        git tag --annotate --message "PR #$(pr_id)" --message "$(< $(ChangelogFile))" $(GitVersion.SemVer)
        git tag --force "$(GitVersion.Major).$(Gitversion.Minor)"
        git $(GitAuth) push --force --verbose --tags
      workingDirectory: $(CheckoutDirectory)
      displayName: Tag latest commit

    - bash: |
        text_blocks=$(cat $(ChangelogFile) | jq -Rs 'split("\n") | map({type: "TextBlock", text: .})')
        echo $text_blocks
        body='
        {
          "type": "message",
          "attachments": [
            {
              "contentType": "application/vnd.microsoft.card.adaptive",
              "contentUrl": null,
              "content": {
                "$schema": "http://adaptivecards.io/schemas/adaptive-card.json",
                "type": "AdaptiveCard",
                "version": "1.2",
                "msteams": {
                  "width": "Full"
                },
                "body": [
                  {
                    "type": "TextBlock",
                    "wrap": true,
                    "text": "A new version $(GitVersion.SemVer) of ${{parameters.terratest_gitversion.resource.repository.name}} has been released."
                  },
                  {
                    "type": "TextBlock",
                    "wrap": true,
                    "text": "Changelog:"
                  }
                ]
              }
            }
          ]
        }'

        clean_body=$(echo "${body}" | jq --argjson textblocks "${text_blocks}" '.attachments[0].content.body |= . + $textblocks')
        echo "Teams Hook body: ${clean_body}"
        curl --verbose --header "ContentType: application/json" --data "${clean_body}" --request POST $(TeamsChannelHookUrl)
      displayName: Push notification to Teams
