trigger: none

resources:
  repositories:
    - repository: terraform-azurerm-brick-waf
      type: git
      name: OTPHU-COE-TEMPLATESPEC/terraform-azurerm-brick-waf
      ref: main
      trigger:
        branches:
          include:
            - main

pool: 
  vmImage: 'ubuntu-latest'

variables:
  System.Debug: true
  CheckoutDirectory: '$(Agent.TempDirectory)/repo'
  #CheckoutUrl: '${{ parameters.tfmod_releaser.resource.repository.remoteUrl}}'
  CheckoutRepositoryOrg: "ADOS-OTPHU-01"
  CheckoutRepositoryName: $(Build.Repository.Name)
  #CheckoutRepositoryProject: '${{ parameters.tfmod_releaser.resource.repository.project.name}}'
  #CheckoutRepository: 'git://$(CheckoutRepositoryProject)/$(CheckoutRepositoryName)'
  TerraformDocsConfigDir: ''
  CustomTerraformDocsConfigDir: '$(CheckoutDirectory)/.config'
  FallbackTerraformDocsConfigDir: '$(Build.SourcesDirectory)/.config'
  TerraformDocsVersion: 0.16.0


stages:
  - stage: tfdocs
    jobs:
    - job: tfdocs
      workspace:
        clean: all

      steps:
        - checkout: self
          clean: true
          persistCredentials: true

        - bash: |
            echo $(Agent.BuildDirectory)
            ehco "CheckoutRepository: $(CheckoutRepository)"
            $(GitAuth) > $(Agent.TempDirectory)/.key
            cat $(Agent.TempDirectory)/.key
            echo "Token: $(System.AccessToken)"
            ls -la $(Agent.TempDirectory)
          displayName: logging

        # - bash: |
        #     cat <<EOF
        #       ${{ convertToJson(parameters.tfmod_releaser) }}
        #     EOF
        #   displayName: Dump webhook trigger payload

        # - task: Cache@2
        #   inputs:
        #     key: linux | "version-$(TerraformDocsVersion)"
        #     path: $(Agent.BuildDirectory)/bin/
        #     cacheHitVar: TerraformDocsReady

        # - bash: |
        #     mkdir -p $(Agent.BuildDirectory)/bin/
        #     cd $(Agent.BuildDirectory)/bin/
        #     curl -vSL https://otpnexus.hu/repository/anonymous-proxy-ra-github.com/terraform-docs/terraform-docs/releases/download/v$(TerraformDocsVersion)/terraform-docs-v$(TerraformDocsVersion)-$(uname)-amd64.tar.gz | tar xz
        #   displayName: Install terraform-docs
        #   condition: ne(variables.TerraformDocsReady, 'true')

        # - bash: |
        #     mkdir -p $(Agent.BuildDirectory)/bin/
        #     cd $(Agent.BuildDirectory)/bin/
        #     curl -vSL https://terraform-docs.io/dl/v$(TerraformDocsVersion)/terraform-docs-v$(TerraformDocsVersion)-$(uname)-amd64.tar.gz | tar xz
        #   displayName: Install terraform-docs
        #   condition: ne(variables.TerraformDocsReady, 'true')

        # - task: Bash@3
        #   inputs:
        #     targetType: 'inline'
        #     script: |
        #       mkdir -p $(CheckoutDirectory)
        #       git clone --progress --verbose https://$(PAT)@dev.azure.com/$(CheckoutRepositoryOrg)/$(CheckoutRepositoryProject)/_git/$(CheckoutRepositoryName)/ $(CheckoutDirectory)
        #   displayName: Checkout repository
        #   env:
        #     PAT: $(PAT)
        
        # - checkout: ${{variables.CheckoutRepositoryName}}

        # - bash: |
        #     mkdir -p $(CustomTerraformDocsConfigDir)
        #     [ ! -e $(CustomTerraformDocsConfigDir)/.terraform-docs.yml ] && cp $(FallbackTerraformDocsConfigDir)/.terraform-docs.yml $(CustomTerraformDocsConfigDir)
        #     [ ! -e $(CustomTerraformDocsConfigDir)/header.txt ] && cp $(FallbackTerraformDocsConfigDir)/header.txt $(CustomTerraformDocsConfigDir)
        #     [ ! -e $(CustomTerraformDocsConfigDir)/footer.txt ] && cp $(FallbackTerraformDocsConfigDir)/footer.txt $(CustomTerraformDocsConfigDir)
        #   displayName: Configure terraform-docs

        # - bash: |
        #     if [[ -d $(CheckoutDirectory)/.test ]]; then
        #       echo ".test directory found"
        #       cat $(CheckoutDirectory)/.test/*.tf | tee $(CustomTerraformDocsConfigDir)/examples.tf
        #     else
        #       echo "No examples available" > $(CustomTerraformDocsConfigDir)/examples.tf
        #     fi
        #   displayName: "Generate examples"

        # - task: Bash@3
        #   inputs:
        #     targetType: 'inline'
        #     script: |
        #       set -ex
        #       $(Agent.BuildDirectory)/bin/terraform-docs \
        #           --config $(CustomTerraformDocsConfigDir)/.terraform-docs.yml \
        #           --output-file $(Agent.TempDirectory)/README.MD \
        #           ./
        #       [ ! -e ./README.MD ] && touch ./README.MD
        #       if ! diff ./README.MD $(Agent.TempDirectory)/README.MD; then
        #         git config user.name "${{ parameters.tfmod_releaser.resource.pushedBy.displayName }}"
        #         git config user.email "${{ parameters.tfmod_releaser.resource.pushedBy.uniqueName }}"
        #         cp $(Agent.TempDirectory)/README.MD ./README.MD
        #         git add README.MD
        #         git commit -m "terraform-docs: autogenerated README.MD +semver: skip"
        #         git push
        #       fi
        #     workingDirectory: $(CheckoutDirectory)
        #   displayName: Generate doc and commit
        #   env:
        #     PAT: $(PAT)

#   - stage: gitversion
#     jobs:
#     - job: gitversion
#       variables:
#         CommitMessage: '${{ parameters.tfmod_releaser.detailedMessage.text }}'
#         GitVersionConfig: '$(Build.SourcesDirectory)/.config/GitVersion.yml'
#         CustomGitVersionConfig: '$(CheckoutDirectory)/.config/GitVersion.yml'
#         FallbackGitVersionConfig: '$(Build.SourcesDirectory)/pipelines/GitVersion.yml'
#         ChangelogFile: $(Agent.TempDirectory)/changelog.md
#       workspace:
#         clean: all

#       steps:
#         - checkout: self
#         - bash: |
#             cat <<EOF
#               ${{ convertToJson(parameters.tfmod_releaser) }}
#             EOF
#           displayName: Dump webhook trigger payload

# ### parse_pr_id ???
#         - bash: |
#             source $(Build.SourcesDirectory)/pipelines/scripts/pr_tools.sh
#             baseUrl="${{parameters.tfmod_releaser.resourceContainers.project.baseUrl}}"
#             project="${{parameters.tfmod_releaser.resource.repository.project.name}}"
#             pr_id=$(parse_pr_id "$(CommitMessage)")
#             get_pr_description "$baseUrl" "$project" "$pr_id" "$(System.AccessToken)" > $(ChangelogFile)
#             echo "PR Id: ${pr_id}"
#             echo "PR Description:"
#             cat $(ChangelogFile)
#             echo "##vso[task.setvariable variable=pr_id;]${pr_id}"
#           failOnStderr: "false"
#           displayName: Fetch PR info

#         - bash: |
#             echo "CheckoutDirectory: $(CheckoutDirectory)"
#             ehco "CheckoutRepository: $(CheckoutRepository)"
#             ehco "Agent.TempDirectory:  $(Agent.TempDirectory)"
#             echo "CustomGitVersionConfig: $(CustomGitVersionConfig)"
#             echo "GitVersionConfig: $(GitVersionConfig)"

#             ls -la $(Agent.TempDirectory)
#           displayName: logging

# ### CHECKOUT ###
#         - task: Bash@3
#           inputs:
#             targetType: 'inline'
#             script: |
#               mkdir -p $(CheckoutDirectory)
#               git clone --progress --verbose https://$(PAT)@dev.azure.com/$(CheckoutRepositoryOrg)/$(CheckoutRepositoryProject)/_git/$(CheckoutRepositoryName)/ $(CheckoutDirectory)
#           displayName: Checkout repository
#           env:
#             PAT: $(PAT)

# ### Generate next version ###
#         - bash: |
#             if [ -e $(CustomGitVersionConfig) ]; then
#               cp $(CustomGitVersionConfig) $(GitVersionConfig)
#               echo "Using ${{ parameters.tfmod_releaser.resource.repository.name }} specific GitVersion config"
#             else
#               cp $(FallbackGitVersionConfig) $(GitVersionConfig)
#               echo "Using common GitVersion config"
#             fi
#             cat $(GitVersionConfig)
#           displayName: Configure GitVersion

#         - task: gitversion/setup@0
#           inputs:
#             versionSpec: "5.x"
#           displayName: Git version setup

#         - task: gitversion/execute@0
#           inputs:
#             targetPath: $(CheckoutDirectory)
#             useConfigFile: true
#             configFilePath: $(GitVersionConfig)
#           displayName: Generate next version

#         - bash: |
#             echo "##vso[build.updatebuildnumber]${{ parameters.tfmod_releaser.resource.repository.name }}_$(GitVersion.MajorMinorPatch)"
#             set -e
#             git config user.name "${{ parameters.tfmod_releaser.resource.pushedBy.displayName }}"
#             git config user.email "${{ parameters.tfmod_releaser.resource.pushedBy.uniqueName }}"
#             git tag --annotate --message "PR #$(pr_id)" --message "$(< $(ChangelogFile))" v$(GitVersion.MajorMinorPatch)
#             git tag --force "v$(GitVersion.Major).$(Gitversion.Minor)"
#             git $(GitAuth) push --force --verbose --tags
#           workingDirectory: $(CheckoutDirectory)
#           displayName: Tag latest commit