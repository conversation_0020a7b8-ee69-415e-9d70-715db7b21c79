jobs:
- job: tfdocs
  # pool: DEV-AksPool-centralagent-Deploy
  variables:
    CheckoutDirectory: '$(Agent.TempDirectory)/repo'
    CheckoutUrl: '${{ parameters.tfmod_releaser.resource.repository.remoteUrl}}'
    GitAuth: '-c http.extraheader="AUTHORIZATION: bearer $(System.AccessToken)"'
    TerraformDocsConfigDir: ''
    CustomTerraformDocsConfigDir: '$(CheckoutDirectory)/.config'
    FallbackTerraformDocsConfigDir: '$(Build.SourcesDirectory)/.config'
    TerraformDocsVersion: 0.16.0
  workspace:
    clean: all

  steps:
    - checkout: self
      clean: true
      persistCredentials: true

    - bash: |
        cat <<EOF
          ${{ convertToJson(parameters.tfmod_releaser) }}
        EOF
      displayName: Dump webhook trigger payload

    - task: Cache@2
      inputs:
        key: linux | "version-$(TerraformDocsVersion)"
        path: $(Agent.BuildDirectory)/bin/
        cacheHitVar: TerraformDocsReady

    # - bash: |
    #     mkdir -p $(Agent.BuildDirectory)/bin/
    #     cd $(Agent.BuildDirectory)/bin/
    #     curl -vSL https://otpnexus.hu/repository/anonymous-proxy-ra-github.com/terraform-docs/terraform-docs/releases/download/v$(TerraformDocsVersion)/terraform-docs-v$(TerraformDocsVersion)-$(uname)-amd64.tar.gz | tar xz
    #   displayName: Install terraform-docs
    #   condition: ne(variables.TerraformDocsReady, 'true')

    - bash: |
        mkdir -p $(Agent.BuildDirectory)/bin/
        cd $(Agent.BuildDirectory)/bin/
        curl -vSL https://terraform-docs.io/dl/v$(TerraformDocsVersion)/terraform-docs-v$(TerraformDocsVersion)-$(uname)-amd64.tar.gz | tar xz
      displayName: Install terraform-docs
      condition: ne(variables.TerraformDocsReady, 'true')

    - bash: |
        rm -rf $(CheckoutDirectory)
        mkdir -p $(CheckoutDirectory)
        git $(GitAuth) clone --progress --verbose $(CheckoutUrl) $(CheckoutDirectory)
      displayName: Checkout repository

    - bash: |
        mkdir -p $(CustomTerraformDocsConfigDir)

        [ ! -e $(CustomTerraformDocsConfigDir)/.terraform-docs.yml ] && cp $(FallbackTerraformDocsConfigDir)/.terraform-docs.yml $(CustomTerraformDocsConfigDir)
        [ ! -e $(CustomTerraformDocsConfigDir)/header.txt ] && cp $(FallbackTerraformDocsConfigDir)/header.txt $(CustomTerraformDocsConfigDir)
        [ ! -e $(CustomTerraformDocsConfigDir)/footer.txt ] && cp $(FallbackTerraformDocsConfigDir)/footer.txt $(CustomTerraformDocsConfigDir)

      displayName: Configure terraform-docs

    - bash: |
        if [[ -d $(CheckoutDirectory)/.test ]]; then
          echo ".test directory found"
          cat $(CheckoutDirectory)/.test/*.tf | tee $(CustomTerraformDocsConfigDir)/examples.tf
        else
          echo "No examples available" > $(CustomTerraformDocsConfigDir)/examples.tf
        fi
      displayName: "Generate examples"

    - bash: |
        set -ex
        $(Agent.BuildDirectory)/bin/terraform-docs \
            --config $(CustomTerraformDocsConfigDir)/.terraform-docs.yml \
            --output-file $(Agent.TempDirectory)/README.MD \
            ./
        [ ! -e ./README.MD ] && touch ./README.MD
        if ! diff ./README.MD $(Agent.TempDirectory)/README.MD; then
          git config user.name "${{ parameters.tfmod_releaser.resource.pushedBy.displayName }}"
          git config user.email "${{ parameters.tfmod_releaser.resource.pushedBy.uniqueName }}"
          cp $(Agent.TempDirectory)/README.MD ./README.MD
          git add README.MD
          git commit -m "terraform-docs: autogenerated README.MD +semver: skip"
          git $(GitAuth) push --verbose
        fi
      workingDirectory: $(CheckoutDirectory)
      displayName: Generate doc and commit