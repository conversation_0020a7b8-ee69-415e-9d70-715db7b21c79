trigger: none

variables:
  System.Debug: true
  CheckoutDirectory: '$(Agent.TempDirectory)/repo'
  CheckoutRepositoryOrg: "ADOS-OTPHU-01"
  CheckoutRepositoryName: '${{ parameters.tfmod_releaser_dev.resource.repository.name}}'
  CheckoutRepositoryProject: '${{ parameters.tfmod_releaser_dev.resource.repository.project.name}}'
  CheckoutRepository: 'git://$(CheckoutRepositoryProject)/$(CheckoutRepositoryName)'
  TerraformDocsConfigDir: ''
  CustomTerraformDocsConfigDir: '$(CheckoutDirectory)/.config'
  FallbackTerraformDocsConfigDir: '$(Build.SourcesDirectory)/.config'
  TerraformDocsVersion: 0.16.0
  
pool: DEV-AksPool-centralagent-Deploy

resources:
  repositories:
    - repository: tooling
      type: git
      name: tooling
      ref: centralagent

  webhooks:
    - webhook: tfmod_releaser_dev
      connection: tfmod_releaser_dev

stages:
  - stage: tfdocs
    jobs:
    - job: tfdocs
      workspace:
        clean: all

      steps:
        - checkout: self
          clean: true
          persistCredentials: true

        - task: Bash@3
          displayName: Dump webhook trigger payload
          inputs:
            targetType: 'inline'
            script: |
              cat <<EOF
                ${{ convertToJson(parameters.tfmod_releaser_dev) }}
              EOF

        - task: Cache@2
          inputs:
            key: linux | "version-$(TerraformDocsVersion)"
            path: $(Agent.BuildDirectory)/bin/
            cacheHitVar: TerraformDocsReady

        - task: Bash@3
          displayName: Install terraform-docs
          inputs:
            targetType: 'inline'
            script: |
              mkdir -p $(Agent.BuildDirectory)/bin/
              cd $(Agent.BuildDirectory)/bin/
              curl -vSL https://otpnexus.hu/repository/anonymous-proxy-ra-github.com/terraform-docs/terraform-docs/releases/download/v$(TerraformDocsVersion)/terraform-docs-v$(TerraformDocsVersion)-$(uname)-amd64.tar.gz | tar xz
          condition: ne(variables.TerraformDocsReady, 'true')

        - task: AzureKeyVault@2
          displayName: Load secrets from Key Vault
          inputs:
            azureSubscription: "DEV-OTP-DD-COEINFDEV-sub-dev-01"
            KeyVaultName: "otp-dd-coeinfdev01"
            SecretsFilter: "PAT-tfmod-releaser"
        
        - task: Bash@3
          displayName: Checkout repository
          inputs:
            targetType: 'inline'
            script: |
              mkdir -p $(CheckoutDirectory)
              git clone --progress --verbose https://$(PAT-tfmod-releaser)@dev.azure.com/$(CheckoutRepositoryOrg)/$(CheckoutRepositoryProject)/_git/$(CheckoutRepositoryName)/ $(CheckoutDirectory)

        - task: Bash@3
          displayName: Configure terraform-docs
          inputs:
            targetType: 'inline'
            script: |
              set -ex
              #Check root dir for config, copy from fallback if not exist.
              mkdir -p $(CustomTerraformDocsConfigDir)
              [ ! -e $(CustomTerraformDocsConfigDir)/.terraform-docs.yml ] && cp $(FallbackTerraformDocsConfigDir)/.terraform-docs.yml $(CustomTerraformDocsConfigDir)
              [ ! -e $(CustomTerraformDocsConfigDir)/header.txt ] && cp $(FallbackTerraformDocsConfigDir)/header.txt $(CustomTerraformDocsConfigDir)
              [ ! -e $(CustomTerraformDocsConfigDir)/footer.txt ] && cp $(FallbackTerraformDocsConfigDir)/footer.txt $(CustomTerraformDocsConfigDir)

              #Check each subfolder for anchor and copy config from root if needed.
              cd $(CheckoutDirectory)
              for dir in `find ./*/* -name anchor.module -exec dirname {} \; | xargs -I {} basename {}`
              do
                echo "inloop"
                echo $dir
                mkdir -p $(CheckoutDirectory)/$dir/.config
                [ ! -e $(CheckoutDirectory)/$dir/.config/.terraform-docs.yml ] && cp $(CustomTerraformDocsConfigDir)/.terraform-docs.yml $(CheckoutDirectory)/$dir/.config/
                [ ! -e $(CheckoutDirectory)/$dir/.config/.header.txt ] && cp $(CustomTerraformDocsConfigDir)/header.txt $(CheckoutDirectory)/$dir/.config/
                [ ! -e $(CheckoutDirectory)/$dir/.config/footer.txt ] && cp $(CustomTerraformDocsConfigDir)/footer.txt $(CheckoutDirectory)/$dir/.config/
                ls -la $(CheckoutDirectory)/$dir/.config
              done

        - task: Bash@3
          displayName: Generate doc and commit
          inputs:
            targetType: 'inline'
            script: |
              set -ex
              for dir in `find . -name anchor.module -exec dirname {} \; | xargs -I {} basename {}`
              do
                [ "$dir" == "." ] && dir=""
                cd $(CheckoutDirectory)/$dir
                mkdir -p $(Agent.TempDirectory)/$dir
                $(Agent.BuildDirectory)/bin/terraform-docs \
                    --config ./.config/.terraform-docs.yml \
                    --output-file $(Agent.TempDirectory)/$dir/USAGE.md \
                    ./
                #debug
                pwd
                ls -la
                #debug
                [ ! -e ./USAGE.md ] && touch ./USAGE.md
                if ! diff ./USAGE.md $(Agent.TempDirectory)/$dir/USAGE.md; then
                  cp $(Agent.TempDirectory)/$dir/USAGE.md ./USAGE.md
                  git add ./USAGE.md
                fi
              done
              if git status --porcelain | grep -q "^[AM]"; then
                git config user.name "${{ parameters.tfmod_releaser_dev.resource.pushedBy.displayName }}"
                git config user.email "${{ parameters.tfmod_releaser_dev.resource.pushedBy.uniqueName }}"
                cd $(CheckoutDirectory)
                git commit -m "terraform-docs: autogenerated USAGE.md +semver: skip"
                git push
              fi
            workingDirectory: $(CheckoutDirectory)

  - stage: gitversion
    jobs:
    - job: gitversion
      variables:
        CommitMessage: '${{ parameters.tfmod_releaser_dev.detailedMessage.text }}'
        GitVersionConfig: '$(Build.SourcesDirectory)/.config/GitVersion.yml'
        CustomGitVersionConfig: '$(CheckoutDirectory)/.config/GitVersion.yml'
        FallbackGitVersionConfig: '$(Build.SourcesDirectory)/pipelines/GitVersion.yml'
        ChangelogFile: $(Agent.TempDirectory)/changelog.md
      workspace:
        clean: all

      steps:
        - checkout: self
          displayName: Checkout self repo

        - task: Bash@3
          displayName: Dump webhook trigger payload
          inputs:
            targetType: 'inline'
            script: |
              cat <<EOF
                ${{ convertToJson(parameters.tfmod_releaser_dev) }}
              EOF

        - task: Bash@3
          displayName: Fetch PR info
          inputs:
            targetType: 'inline'
            script: |
              source $(Build.SourcesDirectory)/pipelines/scripts/pr_tools.sh
              baseUrl="${{parameters.tfmod_releaser_dev.resourceContainers.project.baseUrl}}"
              project="${{parameters.tfmod_releaser_dev.resource.repository.project.name}}"
              pr_id=$(parse_pr_id "$(CommitMessage)")
              get_pr_description "$baseUrl" "$project" "$pr_id" "$(System.AccessToken)" > $(ChangelogFile)
              echo "PR Id: ${pr_id}"
              echo "PR Description:"
              cat $(ChangelogFile)
              echo "##vso[task.setvariable variable=pr_id;]${pr_id}"
            failOnStderr: "false"

        - task: Bash@3
          displayName: logging
          inputs:
            targetType: 'inline'
            script: |
              echo "CheckoutDirectory: $(CheckoutDirectory)"
              ehco "CheckoutRepository: $(CheckoutRepository)"
              ehco "Agent.TempDirectory:  $(Agent.TempDirectory)"
              echo "CustomGitVersionConfig: $(CustomGitVersionConfig)"
              echo "GitVersionConfig: $(GitVersionConfig)"
              ls -la $(Agent.TempDirectory)

### CHECKOUT ###
        - task: AzureKeyVault@2
          displayName: Load secrets from Key Vault
          inputs:
            azureSubscription: "DEV-OTP-DD-COEINFDEV-sub-dev-01"
            KeyVaultName: "otp-dd-coeinfdev01"
            SecretsFilter: "PAT-tfmod-releaser"
        - task: Bash@3
          displayName: Checkout repository
          inputs:
            targetType: 'inline'
            script: |
              mkdir -p $(CheckoutDirectory)
              git clone --progress --verbose https://$(PAT-tfmod-releaser)@dev.azure.com/$(CheckoutRepositoryOrg)/$(CheckoutRepositoryProject)/_git/$(CheckoutRepositoryName)/ $(CheckoutDirectory)

### Generate next version ###
        - task: Bash@3
          displayName: Configure GitVersion
          inputs:
            targetType: 'inline'
            script: |
              if [ -e $(CustomGitVersionConfig) ]; then
                cp $(CustomGitVersionConfig) $(GitVersionConfig)
                echo "Using ${{ parameters.tfmod_releaser_dev.resource.repository.name }} specific GitVersion config"
              else
                cp $(FallbackGitVersionConfig) $(GitVersionConfig)
                echo "Using common GitVersion config"
              fi
              cat $(GitVersionConfig)

        - task: Bash@3
          displayName: Get version
          inputs:
            targetType: 'inline'
            script: |
              gitversion $(CheckoutDirectory) -output buildserver -config $(GitVersionConfig)
            workingDirectory: $(CheckoutDirectory)

        - task: Bash@3
          displayName: Tag latest commit
          inputs:
            targetType: 'inline'
            script: |
              echo "##vso[build.updatebuildnumber]${{ parameters.tfmod_releaser_dev.resource.repository.name }}_$(GitVersion.MajorMinorPatch)"
              set -e
              git config user.name "${{ parameters.tfmod_releaser_dev.resource.pushedBy.displayName }}"
              git config user.email "${{ parameters.tfmod_releaser_dev.resource.pushedBy.uniqueName }}"
              git tag --annotate --message "PR #$(pr_id)" --message "$(< $(ChangelogFile))" v$(GitVersion.MajorMinorPatch)
              git tag --force "v$(GitVersion.Major).$(Gitversion.Minor)"
              git push --force --verbose --tags
            workingDirectory: $(CheckoutDirectory)