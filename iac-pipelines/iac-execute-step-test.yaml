parameters:
  # Common
  - name: environment

  # Terraform configuration
  - name: terraformProjectLocation
  - name: terraformLogLevel
    default: ''

  # Not exposed options
  - name: tfvarsToUseFromPipelineVariables
    type: object
    default: {}
    
  # Auth
  - name: armServiceConnectionName 
### Plan steps
steps:

  # Terraform plan
  - task: AzureCLI@2
    displayName: Terraform test
    name: test
    env:
      ARM_SAS_TOKEN: $(ARM_SAS_TOKEN)
      ARM_USE_CLI: true
      SYSTEM_ACCESSTOKEN: $(System.AccessToken)
      ${{ each var in parameters.tfvarsToUseFromPipelineVariables }}:
        TF_VAR_${{ var.tfvarName }}: $(${{ var.varName }})
    inputs:
      azureSubscription: ${{ parameters.armServiceConnectionName }}
      addSpnToEnvironment: true      
      scriptType: bash
      workingDirectory: "$(Pipeline.Workspace)/source_repo/${{ parameters.terraformProjectLocation }}"
      scriptLocation: inlineScript
      inlineScript: |
        set -eu  # fail on error
        trap "echo Error on line $LINENO" ERR
        echo -e "\n##[section]Federated authentication detected; Token refresh job started\n"        
        trap "kill %1" SIGINT SIGTERM EXIT
        
        set -m
        {
          sleep 540
          while true; do
            token_uri="${SYSTEM_TEAMFOUNDATIONCOLLECTIONURI}${SYSTEM_TEAMPROJECTID}/_apis/distributedtask/hubs/build/plans/${SYSTEM_PLANID}/jobs/${SYSTEM_JOBID}/oidctoken?serviceConnectionId=${AZURESUBSCRIPTION_SERVICE_CONNECTION_ID}&api-version=7.1-preview.1"
            
            set +e
            token=$(curl -X POST --data '' --silent --show-error --fail -H "Authorization: bearer ${SYSTEM_ACCESSTOKEN}" -H "Content-Type: application/json" "${token_uri}&audience=api://AzureADTokenExchange" | jq .oidcToken -r)
            set -e
  
            if [ -z $token ]; then
              echo "\n##[warning]Getting oidcToken failed\n"
            else
              az login --service-principal -u $servicePrincipalId -t $tenantId --federated-token $token --output none
              echo -e "\n##[section]Azure CLI Logged in\n"
  
              pwsh -c "
                \$tokens_json = Get-Content $(Agent.TempDirectory)/.azclitask/msal_token_cache.json | ConvertFrom-Json
                \$tokens_json.AccessToken | gm | Where-Object MemberType -eq NoteProperty | select -ExpandProperty Name | % {\$tokens_json.AccessToken.\$_} | % {
                  [PSCustomObject]@{
                    target     = \$_.target
                    cached_at  = ([DateTime]('1970,1,1')).AddSeconds(\$_.cached_at)
                    expires_on = ([DateTime]('1970,1,1')).AddSeconds(\$_.expires_on)
                  } | fl
                }"
            fi
  
            sleep 540
          done
        } &
        set +m

        export TF_LOG=${{ parameters.terraformLogLevel}}
        TFVARS_PARAMS=""
        GLOBAL_TFVARS="$(Pipeline.Workspace)/tooling_repo/env/${{ parameters.environment }}.tfvars"
        LOCAL_TFVARS="$(Pipeline.Workspace)/source_repo/${{ parameters.terraformProjectLocation }}/${{ parameters.environment }}.tfvars"

        echo "*******************************************************"
        echo "Terraform inputs from tfvars files:"
        if [[ -f "$GLOBAL_TFVARS" ]]; then
          TFVARS_PARAMS="-var-file=$GLOBAL_TFVARS"
          echo 'GLOBAL_TFVARS:'
          cat $GLOBAL_TFVARS; echo
        else
          echo "##vso[task.logissue type=warning]The tfvars file at path $GLOBAL_TFVARS does not exist."
        fi
        if [[ -f "$LOCAL_TFVARS" ]]; then
          TFVARS_PARAMS="$TFVARS_PARAMS -var-file=$LOCAL_TFVARS"
          echo 'LOCAL_TFVARS:'
          cat $LOCAL_TFVARS; echo
        fi
        echo "*******************************************************"
        echo "Terraform inputs from pipeline variables:"
        printenv | grep "TF_VAR_" || true
        echo "*******************************************************"

        set +e
        terraform test -verbose $TFVARS_PARAMS
        retVal=$?
        set -e
        
        exit $retVal
