parameters:
  - name: terraformRCFileForNetworkMirror
  - name: terraformVersion

steps:

  # Copy .terraformrc to user home
  - ${{ if ne(parameters.terraformRCFileForNetworkMirror, '') }}:
    - task: Bash@3
      displayName: Copy terraformrc file for network mirror
      inputs:
        targetType: 'inline'
        workingDirectory: "$(Pipeline.Workspace)/source_repo"
        script: |
          set -eu  # fail on error
          trap "echo Error on line $LINENO" ERR
          cat "${{ parameters.terraformRCFileForNetworkMirror }}" > ~/.terraformrc
          echo "terraformrc contents in user HOME:"
          cat ~/.terraformrc

          export TFENV_REMOTE=https://nexus.prd.azure.otpbank.hu/repository/anonymous-proxy-ra-releases.hashicorp.com
          env | sort

          echo "Starting to set terraform version to ${{ parameters.terraformVersion }}"
          tfenv use ${{ parameters.terraformVersion }}
