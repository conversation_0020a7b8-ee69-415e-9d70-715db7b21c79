parameters:
  - name: destroyResourceGroup
  - name: destroyResourceGroupName
  - name: armServiceConnectionName

steps:

  # Cleanup
  - ${{ if eq(parameters.destroyResourceGroup, true) }}:
    - task: AzureCLI@2
      displayName: 'Cleanup'
      condition: or(failed(), canceled())
      inputs:
        azureSubscription: ${{ parameters.armServiceConnectionName }}
        scriptType: 'bash'
        scriptLocation: 'inlineScript'
        inlineScript: |
          # Use the destroyResourceGroupName parameter
          RESOURCE_GROUP_NAME="${{ parameters.destroyResourceGroupName }}"

          if [ -z "$RESOURCE_GROUP_NAME" ]; then
            echo "The destroy flag is set to true, but no resource group name was provided. Exiting..."
            exit 1
          fi

          # Try to delete the resource group
          echo "Attempting to delete resource group: $RESOURCE_GROUP_NAME"
          az group delete --name "$RESOURCE_GROUP_NAME" --yes --no-wait
