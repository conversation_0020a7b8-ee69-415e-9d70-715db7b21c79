parameters:
  # Common
  - name: taskcondition
    default: succeeded()

  # GCP Auth
  - name: googleCloudProject
  - name: googleCloudRegion
  - name: googleImpersonateServiceAccount

  # Terraform configuration
  - name: terraformProjectLocation
  - name: terraformLogLevel
    default: 'INFO'

  # Terraform CLI options
  - name: terraformCLIGlobalOptionsForApply
  - name: terraformCLIOptionsForApply

  # Plan options
  - name: planFilePath
    default: 'tfplan.out'
  - name: artifactname
    default: 'tfplan'

### Apply steps
steps:
  # Download terraform plan
  - task: DownloadPipelineArtifact@2
    displayName: "Download Terraform plan pipeline artifact"
    condition: ${{ parameters.taskcondition }}
    inputs:
      download: current
      artifact: ${{ parameters.artifactname }}
      path: "$(Pipeline.Workspace)/source_repo/${{ parameters.terraformProjectLocation }}"

  # Terraform apply
  - task: Bash@3
    ${{ if ne(parameters.artifactname, 'tfplandestroy') }}: 
      displayName: Terraform apply plan
    ${{ else }}:
      displayName: Terraform destroy
    condition: ${{ parameters.taskcondition }}
    env:
      SYSTEM_ACCESSTOKEN: $(System.AccessToken)
      GOOGLE_PROJECT: ${{ parameters.googleCloudProject }}
      GOOGLE_REGION: ${{ parameters.googleCloudRegion }}
      GOOGLE_CREDENTIALS: $(downloadGCPKeyFile.secureFilePath)
      GOOGLE_IMPERSONATE_SERVICE_ACCOUNT: ${{ parameters.googleImpersonateServiceAccount }}
    inputs:
      targetType: 'inline'
      workingDirectory: "$(Pipeline.Workspace)/source_repo/${{ parameters.terraformProjectLocation }}"
      script: |
        set -eu  # fail on error
        trap "echo Error on line $LINENO" ERR

        echo "Show terraform plan"
        terraform ${{ parameters.terraformCLIGlobalOptionsForApply }} show ${{ parameters.planFilePath }}

        echo "Applying terraform plan"
        terraform ${{ parameters.terraformCLIGlobalOptionsForApply }} apply ${{ parameters.terraformCLIOptionsForApply }} ${{ parameters.planFilePath }}
        echo "Finished applying terraform plan"