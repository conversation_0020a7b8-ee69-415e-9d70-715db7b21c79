parameters:
  - name: accessToken
    type: string
    default: ''
  - name: prepareGo
    type: boolean
    default: false
  - name: organization
    type: string
    default: ADOS-OTPHU-01
  - name: project
    type: string
    default: OTPHU-COE-TEMPLATESPEC

steps:

  # Clean up gitconfig
  - task: Bash@3
    displayName: Git Cleanup
    inputs:
      targetType: 'inline'
      script: |
        set -e
        ls -la ~/
        if [[ -f ~/.gitconfig ]]; then
          rm ~/.gitconfig
        fi
        ls -la ~/

  # GIT Prepare configuration
  - ${{ if eq(parameters.prepareGo, false) }}: 
    - task: Bash@3
      displayName: Git Prepare
      env:
        SBB_PAT: $(sbb-pat)
      inputs:
        targetType: 'inline'
        script: |
          git config --global http.https://dev.azure.com.proxy ":@***********:8083"
          git config --global url."https://${{ parameters.organization }}:${{ parameters.accessToken }}@dev.azure.com/${{ parameters.organization }}/${{ parameters.project }}".insteadOf https://${{ parameters.organization }}@dev.azure.com/${{ parameters.organization }}/${{ parameters.project }}
          echo "===== git config ====="
          cat ~/.gitconfig

  # GIT Prepare configuration for Go
  - ${{ if eq(parameters.prepareGo, true) }}: 
    - task: Bash@3
      displayName: Git Prepare for Go
      env:
        SBB_PAT: $(sbb-pat)
      inputs:
        targetType: 'inline'
        script: |
          git config --global http.https://dev.azure.com.proxy ":@***********:8083"
          git config --global url."https://${{ parameters.organization }}:${{ parameters.accessToken }}@dev.azure.com/${{ parameters.organization }}/${{ parameters.project }}".insteadOf https://dev.azure.com/${{ parameters.organization }}/${{ parameters.project }}
          echo "===== git config ====="
          cat ~/.gitconfig