parameters:
  - name: env
  - name: appcode
  - name: destroy
  - name: no_proxy

  # Auth
  - name: armServiceConnectionName
  - name: sasTokenLifetimeMinutes
    default: 60     

  # Azure Backend
  - name: storageAccountResourceGroup
  - name: storageAccountName
  - name: storageAccountContainerName
  - name: terraformStateFilePath
  - name: terraformStateFileSnapshot
  - name: storageAccountUseAzureadAuth

  # Terraform configuration
  - name: terraformProjectLocation
  - name: terraformVersion
  - name: terraformExtraNoProxy  
  - name: terraformRCFileForNetworkMirror

  # Go Terratest configuration
  - name: goTerratestLocation
  - name: goProxy
  - name: goSumDB
  - name: goNoSumDB

steps:

  # GIT Prepare configuration for Go
  - template: iac-generic-step-gitprepare.yaml
    parameters:
      accessToken: $(sbb-pat)
      prepareGo: true



  # # Set tfenv terraform version
  # # https://dev.azure.com/ADOS-OTPHU-01/OTPHU-CDO-ADOS-TOOLS/_git/pipelinetemplates?path=/iac-common/step-set-tf-version.yaml
  # - template: iac-common/step-set-tf-version.yaml@pipelinetemplates
  #   parameters:
  #     terraformVersion: ${{ parameters.terraformVersion }}

  # Checkouts
  - checkout: self
    path: "source_repo"
  - checkout: tooling
    path: "tooling_repo"

  # Copy .terraformrc to user home
  - template: iac-generic-step-terraformrc.yaml
    parameters:
      terraformRCFileForNetworkMirror: ${{ parameters.terraformRCFileForNetworkMirror }}
      terraformVersion: ${{ parameters.terraformVersion }}

  - task: AzureCLI@2
    displayName: Get SAS Token
    inputs:
      azureSubscription: ${{ parameters.armServiceConnectionName }}
      scriptType: bash
      scriptLocation: inlineScript
      inlineScript: | 
        set -eu  # fail on error

        export NO_PROXY=$NO_PROXY,${{ parameters.storageAccountName }}.blob.core.windows.net,${{ parameters.terraformExtraNoProxy }}
        export no_proxy=$NO_PROXY

        echo "##vso[task.setvariable variable=NO_PROXY]${NO_PROXY}"
        echo "##vso[task.setvariable variable=no_proxy]${no_proxy}"

        sasToken=$(az storage container generate-sas \
          --account-name ${{ parameters.storageAccountName }} \
          --name ${{ parameters.storageAccountContainerName }} \
          --permissions acdlrw \
          --expiry $(date -u +"%Y-%m-%dT%H:%M:%SZ" -d "+${{ parameters.sasTokenLifetimeMinutes }} minutes") \
          --auth-mode login \
          --as-user \
          -o tsv
        )

        echo "##vso[task.setvariable variable=ARM_SAS_TOKEN;issecret=true]${sasToken}"

  # Set Environment Variables
  - task: Bash@3
    displayName: Set Environment Variables
    name: setEnv
    env:
      ${{ if and(gt(length(parameters.no_proxy), 0),ne(parameters.no_proxy,'')) }}:
        CONCAT_PROXY: $(NO_PROXY),${{ parameters.no_proxy }}
      ${{ else }}:
        CONCAT_PROXY: $(NO_PROXY)
    inputs:
      targetType: 'inline'
      script: |
        echo ------------------------------------------------------------------------------------------
        echo "Exporting environment variables for Terratest"
        echo ------------------------------------------------------------------------------------------
        echo "##vso[task.setvariable variable=iac_environment]${{ parameters.env }}"
        echo "##vso[task.setvariable variable=iac_appcode]${{ parameters.appcode }}"
        echo "##vso[task.setvariable variable=iac_service_connection]${{ parameters.armServiceConnectionName }}"
        echo "##vso[task.setvariable variable=iac_target]${{ parameters.terraformProjectLocation }}"
        echo "##vso[task.setvariable variable=iac_test]${{ coalesce(split(parameters.terraformProjectLocation, '/')[2], split(parameters.terraformProjectLocation, '/')[1]) }}"
        echo "##vso[task.setvariable variable=iac_test_dir]${{ parameters.goTerratestLocation }}"
        echo "##vso[task.setvariable variable=iac_destroy]${{ parameters.destroy }}"
        echo "##vso[task.setvariable variable=iac_storage_rgrp]${{ parameters.storageAccountResourceGroup }}"
        echo "##vso[task.setvariable variable=iac_storage_name]${{ parameters.storageAccountName }}"
        echo "##vso[task.setvariable variable=iac_storage_container]${{ parameters.storageAccountContainerName }}"
        echo "##vso[task.setvariable variable=iac_storage_key]${{ parameters.terraformStateFilePath }}"
        echo "##vso[task.setvariable variable=iac_storage_snapshot]${{ parameters.terraformStateFileSnapshot }}"
        echo "##vso[task.setvariable variable=iac_storage_azuread_auth]${{ parameters.storageAccountUseAzureadAuth }}"
        echo "##vso[task.setvariable variable=iac_source_repo]$(Pipeline.Workspace)/source_repo"
        echo "##vso[task.setvariable variable=iac_tooling_repo]$(Pipeline.Workspace)/tooling_repo"

        echo ------------------------------------------------------------------------------------------
        echo "Exporting environment variables for Go"
        echo ------------------------------------------------------------------------------------------
        echo "##vso[task.setvariable variable=goproxy]${{ parameters.goProxy }}"
        echo "##vso[task.setvariable variable=gosumdb]${{ parameters.goSumDB }}"
        echo "##vso[task.setvariable variable=gonosumdb]${{ parameters.goNoSumDB }}"
        go_path_string=$(go env | grep GOPATH)
        IFS='"' read -ra go_path_arr <<< $go_path_string
        go_bin_path=${go_path_arr[1]}/bin
        echo $go_bin_path
        echo "##vso[task.setvariable variable=GoBin]$go_bin_path"

        echo ------------------------------------------------------------------------------------------
        echo "Exporting environment variables for Proxy"
        echo ------------------------------------------------------------------------------------------
        echo CONCAT_PROXY: $CONCAT_PROXY
        unset NO_PROXY
        unset no_proxy
        unset NOPROXY
        unset noproxy
        export NO_PROXY=$CONCAT_PROXY

        echo ------------------------------------------------------------------------------------------
        echo Exported Environment variables
        echo ------------------------------------------------------------------------------------------
        for var in $(compgen -e); do
          echo $var ${!var};
        done
        echo ------------------------------------------------------------------------------------------
        echo NO_PROXY: $NO_PROXY
        echo PARAM_NO_PROXY: ${{ parameters.no_proxy }}
        echo HTTP_PROXY: $HTTP_PROXY
        echo HTTPS_PROXY: $HTTPS_PROXY
        echo ------------------------------------------------------------------------------------------
