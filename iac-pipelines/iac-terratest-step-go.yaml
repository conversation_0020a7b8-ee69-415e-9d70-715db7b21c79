parameters:
  - name: terraformProjectLocation
  - name: goTerratestLocation

steps:

  # Go Version
  - task: Go@0
    inputs:
      command: "version"
    displayName: Run 'go version'

  # Go Env
  - task: Go@0
    inputs:
      command: "env"
    displayName: Run 'go env'

  # Checking if Go module is initialized.
  - task: Bash@3
    displayName: Checking if 'go.mod' file is present
    name: goModPresence
    inputs:
      targetType: 'inline'
      workingDirectory: "$(Pipeline.Workspace)/source_repo/${{ parameters.goTerratestLocation }}"
      script: |
        GOMODPRESENCE=$(ls -la | grep go.mod)
        if [ "$GOMODPRESENCE" = "" ]; then
          echo "##vso[task.setvariable variable=GoModuleState]not_initiated"
        fi

  # Go mod init
  - task: Bash@3
    displayName: Run 'go mod init'
    name: goModInit
    condition: eq(variables.GoModuleState, 'not_initiated')
    inputs:
      targetType: 'inline'
      workingDirectory: "$(Pipeline.Workspace)/source_repo/${{ parameters.goTerratestLocation }}"
      script: |
        go mod init "SBB/$(Build.Repository.Name)/${{ coalesce(split(parameters.terraformProjectLocation, '/')[2], split(parameters.terraformProjectLocation, '/')[1]) }}"

  # Go mod tidy
  - task: Bash@3
    displayName: Run 'go mod tidy'
    condition: eq(variables.GoModuleState, 'not_initiated')
    name: goModTidy
    inputs:
      targetType: 'inline'
      workingDirectory: "$(Pipeline.Workspace)/source_repo/${{ parameters.goTerratestLocation }}"
      script: |
        go mod tidy

  # Download Go modules (dependencies)
  - task: Bash@3
    displayName: Run 'go mod download'
    name: goModDownload
    inputs:
      targetType: 'inline'
      workingDirectory: "$(Pipeline.Workspace)/source_repo/${{ parameters.goTerratestLocation }}"
      script: |
        go mod download -x

  # Install tools
  - task: Bash@3
    displayName: Install 'go-junit-report' Tool
    name: goJUnitReport
    inputs:
      targetType: 'inline'
      workingDirectory: "$(Pipeline.Workspace)/source_repo/${{ parameters.goTerratestLocation }}"
      script: |
        go mod download -x github.com/jstemmer/go-junit-report@v1.0.0
        go install github.com/jstemmer/go-junit-report@v1.0.0

  # GIT Prepare configuration
  - template: iac-generic-step-gitprepare.yaml
    parameters:
      accessToken: $(sbb-pat)

  # Git commit & push
  - task: Bash@3
    displayName: Run 'git commit' & 'push'
    name: gitCommitPush
    inputs:
      targetType: 'inline'
      workingDirectory: "$(Pipeline.Workspace)/source_repo"
      script: |
        echo "Configure git user.name, user.email"
        git config --global user.email "<EMAIL>"
        git config --global user.name "CI Agent"

        if [ -n "$SYSTEM_PULLREQUEST_SOURCEBRANCH" ]
        then
          branch=`echo $SYSTEM_PULLREQUEST_SOURCEBRANCH | cut -d/ -f 3-`
          echo "This is a PR. Branch name: "$branch
        else
          branch=`echo $BUILD_SOURCEBRANCH | cut -d/ -f 3-`
          echo "This is not a PR. Branch name: "$branch
        fi

        echo "Switch to target branch"
        git switch -c $branch

        echo "Show branches"
        git branch

        echo "Stage git changes"
        git add -A

        # Check if there are any changes to commit
        if git diff --staged --quiet; then
          echo "No changes to commit. Skipping commit and push."
        else
          echo "Commit git changes"
          git commit -m "***NO_CI***"

          echo "Push git changes"
          git push -f origin HEAD
        fi
