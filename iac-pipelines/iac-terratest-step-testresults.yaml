parameters:
  - name: goTerratestLocation

steps:

  # JUnitXML Test report task
  - task: Bash@3
    displayName: Create JUnitXML test report
    condition: always()
    inputs:
      targetType: 'inline'
      workingDirectory: "$(Pipeline.Workspace)/source_repo/${{ parameters.goTerratestLocation }}"
      script: |
         cat test-logs.txt | $(GoBin)/go-junit-report > terratest-junit-report.xml

  - task: PublishTestResults@2
    displayName: Publish JUnitXML test report
    condition: always()
    inputs:
      testResultsFormat: 'JUnit'
      testResultsFiles: '**/terratest-junit-report.xml'
