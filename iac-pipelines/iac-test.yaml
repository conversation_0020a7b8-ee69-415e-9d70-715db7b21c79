parameters:
  # Common
  - name: environment
  - name: appcode
  - name: resource_group_name
    default: ''
  - name: no_proxy
    default: ''
  - name: timeoutInMinutes
    type: number
    default: 60

  # Auth
  - name: armServiceConnectionName

  # Azure Backend
  - name: storageAccountResourceGroup
  - name: storageAccountName
  - name: storageAccountContainerName
  - name: terraformStateFileSnapshot
    default: true
  - name: storageAccountUseAzureadAuth
    default: true

  # Terraform configuration
  - name: terraformProjectLocation
  - name: terraformVersion
    default: '1.7.4'
  - name: terraformExtraNoProxy
    default: ''
  - name: terraformRCFileForNetworkMirror
    default: '$(Pipeline.Workspace)/tooling_repo/.config/.terraformrc'
  - name: terraformUnlockStateLockID
    default: ' '
  - name: terraformLogLevel
    default: ''

  # Terraform CLI options
  # Init
  - name: terraformCLIGlobalOptionsForInit
    default: ''
  - name: terraformCLIOptionsForInit
    default: ''

extends:
  # https://dev.azure.com/ADOS-OTPHU-01/OTPHU-CDO-ADOS-TOOLS/_git/pipelinetemplates?path=/cd-common/pipeline-generic.yaml
  template: cd-common/pipeline-generic.yaml@pipelinetemplates
  parameters:
    agentPoolNamePostfix: "AksPool-centralagent-Deploy"
    environment: ${{ split(parameters.environment, '-')[0] }}
    appCode: ${{ parameters.appcode }}
    pipelinetemplateRepoReference: self
    setVersionNumberToGitTagForRelease: false

###########################################
#### Deployment Stage - Terraform test ####
###########################################
    deploymentStageJobTimeoutInMinutes: ${{ parameters.timeoutInMinutes}}

    ### deploy -1 - keyvault
    # keyVaultServiceConnectionName:
    # KeyVaultName:
    # keyVaultCommaSeparatedSecretNames:
    # keyVaultRunAsPreJob:

    ### deploy 0
    stepsPrePipeline:
      ## Terraform init steps
      - template: iac-execute-step-init.yaml
        parameters:
          # Auth
          armServiceConnectionName: ${{ parameters.armServiceConnectionName }}

          #Azure Backend
          storageAccountResourceGroup: ${{ parameters.storageAccountResourceGroup }}
          storageAccountName: ${{ parameters.storageAccountName }}
          storageAccountContainerName: ${{ parameters.storageAccountContainerName }}
          terraformStateFilePath: ${{ format('{0}.{1}.tfstate', variables['Build.Repository.Name'], coalesce(split(parameters.terraformProjectLocation, '/')[2], split(parameters.terraformProjectLocation, '/')[1])) }}
          terraformStateFileSnapshot: ${{ parameters.terraformStateFileSnapshot }}
          storageAccountUseAzureadAuth: ${{ parameters.storageAccountUseAzureadAuth }}

          # Terraform configuration
          terraformProjectLocation: ${{ parameters.terraformProjectLocation }}
          terraformVersion: ${{ parameters.terraformVersion }}
          terraformExtraNoProxy: ${{ parameters.terraformExtraNoProxy }}
          terraformRCFileForNetworkMirror: ${{ parameters.terraformRCFileForNetworkMirror }}
          ${{ if ne(parameters.terraformUnlockStateLockID, ' ') }}:
            terraformUnlockStateLockID: ${{ parameters.terraformUnlockStateLockID }}

          # Terraform CLI options
          terraformCLIGlobalOptionsForInit: ${{ parameters.terraformCLIGlobalOptionsForInit }}
          terraformCLIOptionsForInit: ${{ parameters.terraformCLIOptionsForInit }}

          # Not exposed options
          publishArtifact: true

    ### deploy 1
    stepsApplicationDeployment:
      ## Terraform test steps
      - template: iac-execute-step-test.yaml
        parameters:
          # Common
          environment: ${{ parameters.environment }}

          # Auth
          armServiceConnectionName: ${{ parameters.armServiceConnectionName }}          

          # Terraform configuration
          terraformProjectLocation: ${{ parameters.terraformProjectLocation }}
          terraformLogLevel: ${{ parameters.terraformLogLevel }}

          # Not exposed options
          tfvarsToUseFromPipelineVariables: 
            - tfvarName: owner
              varName: Build.QueuedBy
            - tfvarName: owner_dl
              varName: <EMAIL>

    ### deploy 2
    # stepsPostApplicationDeployment:

    ### deploy 3
    # stepsPostPipeline:
