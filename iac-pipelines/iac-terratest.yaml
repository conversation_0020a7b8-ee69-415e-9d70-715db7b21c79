parameters:
  # Common
  - name: environment
  - name: appcode
  - name: destroy
    type: boolean
    default: true 
  - name: destroyResourceGroup
    type: boolean
    default: false
  - name: destroyResourceGroupName
  - name: no_proxy
    default: ''
  - name: timeout_in_minutes
    type: number
    default: 60

  # Auth
  - name: armServiceConnectionName

  # Azure Backend
  - name: storageAccountResourceGroup
  - name: storageAccountName
  - name: storageAccountContainerName
  - name: terraformStateFileSnapshot
    default: true
  - name: storageAccountUseAzureadAuth
    default: true
  
  # Keyvault
  - name: keyVaultServiceConnectionName
  - name: keyVaultName
  - name: keyVaultCommaSeparatedSecretNames
  - name: keyVaultRunAsPreJob
    type: boolean
    default: true

  # Terraform configuration
  - name: terraformProjectLocation
  - name: terraformExtraNoProxy
    default: ''
  - name: terraformVersion
    default: '1.7.4'
  - name: terraformRCFileForNetworkMirror
    default: '$(Pipeline.Workspace)/tooling_repo/.config/.terraformrc'

  # Terratest configuration
  - name: goTerratestLocation
  - name: goProxy
    default: https://nexus.prd.azure.otpbank.hu/repository/anonymous-proxy-go-proxy.golang.org/,direct
  - name: goSumDB
    default: off
  - name: goNoSumDB
    default: dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/


extends:
  # https://dev.azure.com/ADOS-OTPHU-01/OTPHU-CDO-ADOS-TOOLS/_git/pipelinetemplates?path=/cd-common/pipeline-generic.yaml
  template: cd-common/pipeline-generic.yaml@pipelinetemplates
  parameters:
    pipelinetemplateRepoReference: self
    agentPoolNamePostfix: "AksPool-centralagent-Deploy"
    environment: ${{ split(parameters.environment, '-')[0] }}
    appCode: ${{ parameters.appcode }}

#########################################
#### Deployment Stage - Go Terratest ####
#########################################
    deploymentStageJobTimeoutInMinutes: ${{ parameters.timeout_in_minutes}}

    ### deploy -1 - keyvault
    keyVaultServiceConnectionName: ${{ parameters.keyVaultServiceConnectionName }}
    KeyVaultName: ${{ parameters.keyVaultName }}
    keyVaultCommaSeparatedSecretNames: ${{ parameters.keyVaultCommaSeparatedSecretNames }}
    keyVaultRunAsPreJob: ${{ parameters.keyVaultRunAsPreJob }}

    ### deploy 0
    stepsPrePipeline:
      ## Set up environment
      - template: iac-terratest-step-init.yaml
        parameters:
          env: ${{ parameters.environment }}
          appcode: ${{ parameters.appcode }}
          destroy: ${{ parameters.destroy }}
          no_proxy: ${{ parameters.no_proxy }}
          armServiceConnectionName: ${{ parameters.armServiceConnectionName }}
          storageAccountResourceGroup: ${{ parameters.storageAccountResourceGroup }}
          storageAccountName: ${{ parameters.storageAccountName }}
          storageAccountContainerName: ${{ parameters.storageAccountContainerName }}
          terraformStateFilePath: ${{ format('{0}.{1}.tfstate', variables['Build.Repository.Name'], coalesce(split(parameters.terraformProjectLocation, '/')[2], split(parameters.terraformProjectLocation, '/')[1])) }}
          terraformStateFileSnapshot: ${{ parameters.terraformStateFileSnapshot }}
          storageAccountUseAzureadAuth: ${{ parameters.storageAccountUseAzureadAuth }}
          terraformProjectLocation: ${{ parameters.terraformProjectLocation }}
          terraformVersion: ${{ parameters.terraformVersion }}
          terraformRCFileForNetworkMirror: ${{ parameters.terraformRCFileForNetworkMirror }}
          terraformExtraNoProxy: ${{ parameters.terraformExtraNoProxy }}
          goTerratestLocation: ${{ parameters.goTerratestLocation }}
          goProxy: ${{ parameters.goProxy }}
          goSumDB: ${{ parameters.goSumDB }}
          goNoSumDB: ${{ parameters.goNoSumDB }}

      ## Set up Go
      - template: iac-terratest-step-go.yaml
        parameters:
          terraformProjectLocation: ${{ parameters.terraformProjectLocation }}
          goTerratestLocation: ${{ parameters.goTerratestLocation }}

    ### deploy 1
    stepsApplicationDeployment:
      ## Run Go Terratest
      - template: iac-terratest-step-run.yaml
        parameters:
          no_proxy: ${{ parameters.no_proxy }}
          armServiceConnectionName: ${{ parameters.armServiceConnectionName }}
          goTerratestLocation: ${{ parameters.goTerratestLocation }}
          goTerratestTimeout: ${{ parameters.timeout_in_minutes }}

    ### deploy 2
    stepsPostApplicationDeployment:
      ## Run Cleanup
      - template: iac-terratest-step-cleanup.yaml
        parameters:
          destroyResourceGroup: ${{ parameters.destroyResourceGroup }}
          destroyResourceGroupName: ${{ parameters.destroyResourceGroupName }}
          armServiceConnectionName: ${{ parameters.armServiceConnectionName }}

    ### deploy 3
    stepsPostPipeline:
    ## Publish Test Results
    - template: iac-terratest-step-testresults.yaml
      parameters:
        goTerratestLocation: ${{ parameters.goTerratestLocation }}
