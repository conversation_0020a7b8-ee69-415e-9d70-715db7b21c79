parameters:
  # Service Connection
  - name: armServiceConnectionName
  - name: sasTokenLifetimeMinutes
    default: 540

  # Azure Backend
  - name: storageAccountResourceGroup
  - name: storageAccountName
  - name: storageAccountContainerName
  - name: terraformStateFilePath
  - name: terraformStateFileSnapshot
  - name: storageAccountUseAzureadAuth

  # Terraform configuration
  - name: terraformProjectLocation
  - name: terraformVersion
  - name: terraformExtraNoProxy
  - name: terraformRCFileForNetworkMirror
  - name: terraformUnlockStateLockID
    default: ''

  # Terraform CLI options
  - name: terraformCLIGlobalOptionsForInit
  - name: terraformCLIOptionsForInit

  # Not exposed options
  - name: publishArtifact
    default: false

### Init steps
steps:

  # GIT insteadOf configuration
  # https://dev.azure.com/ADOS-OTPHU-01/OTPHU-CDO-ADOS-TOOLS/_git/pipelinetemplates?path=/iac-common/step-tf-git-prepare.yaml
  - template: iac-common/step-tf-git-prepare.yaml@pipelinetemplates
    parameters:
      terraformModulesAccessPAT: $(TERRAFORM_MODULES_ACCESS_PAT)

  # Azure Login
  # https://dev.azure.com/ADOS-OTPHU-01/OTPHU-CDO-ADOS-TOOLS/_git/pipelinetemplates?path=/common/step-wif-serviceconnection-login.yaml
  - template: common/step-wif-serviceconnection-login.yaml@pipelinetemplates
    parameters:
      armServiceConnection: ${{ parameters.armServiceConnectionName }}

  # Checkouts
  - checkout: self
    path: "source_repo"
  - checkout: tooling
    path: "tooling_repo"

  # Download lock artifact for deploy stage (publishArtifact=false)
  - ${{ if eq(parameters.publishArtifact, false) }}:
    - task: DownloadPipelineArtifact@2
      displayName: Download terraform lock file
      inputs:
        targetPath: $(Pipeline.Workspace)/source_repo/${{ parameters.terraformProjectLocation }}
        artifact: tflock

  # Copy .terraformrc to user home
  - template: iac-generic-step-terraformrc.yaml
    parameters:
      terraformRCFileForNetworkMirror: ${{ parameters.terraformRCFileForNetworkMirror }}
      terraformVersion: ${{ parameters.terraformVersion }}

  - task: AzureCLI@2
    displayName: Get SAS Token
    inputs:
      azureSubscription: ${{ parameters.armServiceConnectionName }}
      scriptType: bash
      scriptLocation: inlineScript
      inlineScript: | 
        set -eu  # fail on error

        export NO_PROXY=$NO_PROXY,${{ parameters.storageAccountName }}.blob.core.windows.net,${{ parameters.terraformExtraNoProxy }}
        export no_proxy=$NO_PROXY

        echo "##vso[task.setvariable variable=NO_PROXY]${NO_PROXY}"
        echo "##vso[task.setvariable variable=no_proxy]${no_proxy}"

        sasToken=$(az storage container generate-sas \
          --account-name ${{ parameters.storageAccountName }} \
          --name ${{ parameters.storageAccountContainerName }} \
          --permissions acdlrw \
          --expiry $(date -u +"%Y-%m-%dT%H:%M:%SZ" -d "+${{ parameters.sasTokenLifetimeMinutes }} minutes") \
          --auth-mode login \
          --as-user \
          -o tsv
        )

        echo "##vso[task.setvariable variable=ARM_SAS_TOKEN;issecret=true]${sasToken}"

  # # Set tfenv terraform version
  # # https://dev.azure.com/ADOS-OTPHU-01/OTPHU-CDO-ADOS-TOOLS/_git/pipelinetemplates?path=/iac-common/step-set-tf-version.yaml
  # - template: iac-common/step-set-tf-version.yaml@pipelinetemplates
  #   parameters:
  #     terraformVersion: ${{ parameters.terraformVersion }}

  # Terraform init
  - task: Bash@3
    displayName: Terraform init
    name: prepare
    env:
      ARM_SAS_TOKEN: $(ARM_SAS_TOKEN)
    inputs:
      targetType: 'inline'
      workingDirectory: "$(Pipeline.Workspace)/source_repo/${{ parameters.terraformProjectLocation }}"
      script: |
        set -eu  # fail on error
        trap "echo Error on line $LINENO" ERR

        export TERRAFORMVERSION=${{ parameters.terraformVersion }}
        export TERRAFORM_VERSION=${{ parameters.terraformVersion }}
        NO_PROXY_TEMP=$NO_PROXY,${{ parameters.terraformExtraNoProxy }}
        export NO_PROXY=$(echo $NO_PROXY_TEMP | sed "s/,\*.core.windows.net//" | sed "s/,vault.azure.net//")
        export no_proxy=$NO_PROXY
        echo "All environment variables:"
        env | sort
        echo "##vso[task.setvariable variable=NO_PROXY]${NO_PROXY}"
        echo "##vso[task.setvariable variable=no_proxy]${no_proxy}"

        export ARM_USE_AZUREAD=true
        echo "##vso[task.setvariable variable=ARM_USE_AZUREAD]${ARM_USE_AZUREAD}"

        echo "Initializing terraform"
        terraform ${{ parameters.terraformCLIGlobalOptionsForInit }} init \
          -backend-config="resource_group_name=${{ parameters.storageAccountResourceGroup }}" \
          -backend-config="storage_account_name=${{ parameters.storageAccountName }}" \
          -backend-config="container_name=${{ parameters.storageAccountContainerName }}" \
          -backend-config="key=${{ parameters.terraformStateFilePath }}" \
          -backend-config="snapshot=${{ parameters.terraformStateFileSnapshot }}" \
          -backend-config="use_azuread_auth=${{ parameters.storageAccountUseAzureadAuth }}" ${{ parameters.terraformCLIOptionsForInit }}

  # Unlock statefile
  - ${{ if ne(parameters.terraformUnlockStateLockID, '') }}:
    - task: Bash@3
      displayName: Terraform unlocking locked state
      env:
        ARM_SAS_TOKEN: $(ARM_SAS_TOKEN)
      inputs:
        targetType: 'inline'
        workingDirectory: "$(Pipeline.Workspace)/source_repo/${{ parameters.terraformProjectLocation }}"
        script: |
          set -eu  # fail on error
          trap "echo Error on line $LINENO" ERR

          echo "Unlocking locked state file with ID: ${{ parameters.terraformUnlockStateLockID }}"
          terraform ${{ parameters.terraformCLIGlobalOptionsForInit }} force-unlock -force ${{ parameters.terraformUnlockStateLockID }}
          echo "Finished unlocking locked state file"

  # Publish lock artifact for init stage (publishArtifact=true)
  - ${{ if eq(parameters.publishArtifact, true) }}:
    - task: PublishPipelineArtifact@1
      displayName: "Publish terraform lock file"
      inputs:
        targetPath: $(Pipeline.Workspace)/source_repo/${{ parameters.terraformProjectLocation }}/.terraform.lock.hcl
        artifact: tflock
