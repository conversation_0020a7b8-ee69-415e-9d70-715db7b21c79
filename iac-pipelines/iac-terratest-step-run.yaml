parameters:
  - name: no_proxy
  - name: armServiceConnectionName
  - name: goTerratestLocation
  - name: goTerratestTimeout

steps:

  # Running Terratest
  - task: AzureCLI@2
    displayName: Run Terratest
    env:
      ARM_SAS_TOKEN: $(ARM_SAS_TOKEN)
      ARM_USE_CLI: true
      SYSTEM_ACCESSTOKEN: $(System.AccessToken)
      ARM_OIDC_REQUEST_TOKEN: $(System.AccessToken)
      ARM_USE_OIDC: true
      ${{ if and(gt(length(parameters.no_proxy), 0),ne(parameters.no_proxy,'')) }}:
        CONCAT_PROXY: $(NO_PROXY),${{ parameters.no_proxy }}
      ${{ else }}:
        CONCAT_PROXY: $(NO_PROXY)
    inputs:
      azureSubscription: ${{ parameters.armServiceConnectionName }}
      addSpnToEnvironment: true
      scriptType: 'bash'
      scriptLocation: 'inlineScript'
      workingDirectory: "$(Pipeline.Workspace)/source_repo/${{ parameters.goTerratestLocation }}"
      inlineScript: |
        set -e
        trap "echo Error on line $LINENO" ERR
        echo -e "\n##[section]Federated authentication detected; Token refresh job started\n"        
        trap "kill %1" SIGINT SIGTERM EXIT 

        echo ------------------------------------------------------------------------------------------
        echo "Token refresh cycle started"
        echo ------------------------------------------------------------------------------------------
        set -m
        {
          sleep 540
          while true; do
            token_uri="${SYSTEM_TEAMFOUNDATIONCOLLECTIONURI}${SYSTEM_TEAMPROJECTID}/_apis/distributedtask/hubs/build/plans/${SYSTEM_PLANID}/jobs/${SYSTEM_JOBID}/oidctoken?serviceConnectionId=${AZURESUBSCRIPTION_SERVICE_CONNECTION_ID}&api-version=7.1-preview.1"
            
            set +e
            token=$(curl -X POST --data '' --silent --show-error --fail -H "Authorization: bearer ${SYSTEM_ACCESSTOKEN}" -H "Content-Type: application/json" "${token_uri}&audience=api://AzureADTokenExchange" | jq .oidcToken -r)
            set -e
  
            if [ -z $token ]; then
              echo "\n##[warning]Getting oidcToken failed\n"
            else
              az login --service-principal -u $servicePrincipalId -t $tenantId --federated-token $token --output none
              echo -e "\n##[section]Azure CLI Logged in\n"
  
              pwsh -c "
                \$tokens_json = Get-Content $(Agent.TempDirectory)/.azclitask/msal_token_cache.json | ConvertFrom-Json
                \$tokens_json.AccessToken | gm | Where-Object MemberType -eq NoteProperty | select -ExpandProperty Name | % {\$tokens_json.AccessToken.\$_} | % {
                  [PSCustomObject]@{
                    target     = \$_.target
                    cached_at  = ([DateTime]('1970,1,1')).AddSeconds(\$_.cached_at)
                    expires_on = ([DateTime]('1970,1,1')).AddSeconds(\$_.expires_on)
                  } | fl
                }"
            fi
  
            sleep 540
          done
        } &
        set +m
        echo ------------------------------------------------------------------------------------------
        echo "Exporting environment variables"
        echo ------------------------------------------------------------------------------------------
        echo CONCAT_PROXY: $CONCAT_PROXY
        unset NO_PROXY
        unset no_proxy
        unset NOPROXY
        unset noproxy
        export NO_PROXY=$CONCAT_PROXY
        export TF_VAR_owner="$(Build.QueuedBy)"
        export TF_VAR_owner_dl="<EMAIL>"
        export ARM_CLIENT_ID=$servicePrincipalId
        export ARM_CLIENT_SECRET=$servicePrincipalKey
        export ARM_SUBSCRIPTION_ID=`az account show -o json | jq .id | tr -d '"'`
        export ARM_TENANT_ID=$tenantId
        echo ------------------------------------------------------------------------------------------
        echo Exported Environment variables
        echo ------------------------------------------------------------------------------------------
        for var in $(compgen -e); do
          echo $var ${!var};
        done
        echo ------------------------------------------------------------------------------------------
        echo NO_PROXY: $NO_PROXY
        echo PARAM_NO_PROXY: ${{ parameters.no_proxy }}
        echo HTTP_PROXY: $HTTP_PROXY
        echo HTTPS_PROXY: $HTTPS_PROXY
        echo ------------------------------------------------------------------------------------------
        echo "Running Terratest"
        echo ------------------------------------------------------------------------------------------
        go test -v -timeout ${{ parameters.goTerratestTimeout }}m 2>&1 $@ | tee test-logs.txt
        TT_EXITCODE=${PIPESTATUS[0]}
        echo "Terratest exit code: $TT_EXITCODE"
        echo " 🔽 ### ### ### ### ### ###    Test Summary    ### ### ### ### ### ### 🔽 "
        cat test-logs.txt | grep "\--- "
        echo " 🔼 ### ### ### ### ### ### ### ########## ### ### ### ### ### ### ### 🔼 "
        if [ $TT_EXITCODE -ne 0 ]; then
          echo "Terratest failed with exit code $TT_EXITCODE"
          exit $TT_EXITCODE
        fi
        echo ------------------------------------------------------------------------------------------
        echo "Finished running Terratest"
        echo ------------------------------------------------------------------------------------------
