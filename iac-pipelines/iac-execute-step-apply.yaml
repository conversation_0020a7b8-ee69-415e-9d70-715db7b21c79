parameters:
  # Terraform configuration
  - name: terraformProjectLocation
  - name: taskcondition
    default: succeeded()

  # Terraform CLI options
  - name: terraformCLIGlobalOptionsForApply
  - name: terraformCLIOptionsForApply
  - name: terraformLogLevel
    default: 'ERROR'

  # Plan options
  - name: planFilePath
    default: 'tfplan.out'
  - name: artifactname
    default: 'tfplan'

  # Auth
  - name: armServiceConnectionName
### Apply steps
steps:
  # Download terraform plan
  - task: DownloadPipelineArtifact@2
    displayName: "Download Terraform plan pipeline artifact"
    condition: ${{ parameters.taskcondition }}
    inputs:
      download: current
      artifact: ${{ parameters.artifactname }}
      path: "$(Pipeline.Workspace)/source_repo/${{ parameters.terraformProjectLocation }}"

  # Terraform apply
  - task: AzureCLI@2
    ${{ if ne(parameters.artifactname, 'tfplandestroy') }}: 
      displayName: Terraform apply plan
    ${{ else }}:
      displayName: Terraform destroy
    condition: ${{ parameters.taskcondition }}
    env:
      ARM_SAS_TOKEN: $(ARM_SAS_TOKEN)
      ARM_USE_CLI: true
      SYSTEM_ACCESSTOKEN: $(System.AccessToken)
    inputs:
      azureSubscription: ${{ parameters.armServiceConnectionName }}
      addSpnToEnvironment: true
      scriptType: bash
      workingDirectory: "$(Pipeline.Workspace)/source_repo/${{ parameters.terraformProjectLocation }}"
      scriptLocation: inlineScript
      inlineScript: |
        set -eu  # fail on error
        export TF_LOG=${{ parameters.terraformLogLevel}}
        trap "echo Error on line $LINENO" ERR
        echo -e "\n##[section]Federated authentication detected; Token refresh job started\n"
        
        trap "kill %1" SIGINT SIGTERM EXIT
        set -m
        {
          sleep 540
          while true; do
            token_uri="${SYSTEM_TEAMFOUNDATIONCOLLECTIONURI}${SYSTEM_TEAMPROJECTID}/_apis/distributedtask/hubs/build/plans/${SYSTEM_PLANID}/jobs/${SYSTEM_JOBID}/oidctoken?serviceConnectionId=${AZURESUBSCRIPTION_SERVICE_CONNECTION_ID}&api-version=7.1-preview.1"
            set +e
            token=$(curl -X POST --data '' --silent --show-error --fail -H "Authorization: bearer ${SYSTEM_ACCESSTOKEN}" -H "Content-Type: application/json" "${token_uri}&audience=api://AzureADTokenExchange" | jq .oidcToken -r)
            set -e
           if [ -z $token ]; then
              echo "\n##[warning]Getting oidcToken failed\n"
            else
              echo -n $token > $(Build.SourcesDirectory)/oidc-token
              az login --service-principal -u $servicePrincipalId -t $tenantId --federated-token $token --output none
              echo -e "\n##[section]Azure CLI Logged in\n"
             pwsh -c "
                \$tokens_json = Get-Content $(Agent.TempDirectory)/.azclitask/msal_token_cache.json | ConvertFrom-Json
                \$tokens_json.AccessToken | gm | Where-Object MemberType -eq NoteProperty | select -ExpandProperty Name | % {\$tokens_json.AccessToken.\$_} | % {
                  [PSCustomObject]@{
                    target     = \$_.target
                    cached_at  = ([DateTime]('1970,1,1')).AddSeconds(\$_.cached_at)
                    expires_on = ([DateTime]('1970,1,1')).AddSeconds(\$_.expires_on)
                  } | fl
                }"
            fi
           sleep 540
          done
        } &
        set +m

        echo "Show terraform plan"
        terraform ${{ parameters.terraformCLIGlobalOptionsForApply }} show ${{ parameters.planFilePath }}

        echo "Applying terraform plan"
        terraform ${{ parameters.terraformCLIGlobalOptionsForApply }} apply ${{ parameters.terraformCLIOptionsForApply }} ${{ parameters.planFilePath }}
        echo "Finished applying terraform plan"
