trigger: none

appendCommitMessageToRunName: false
name: $(date:yyyyMMdd)$(rev:.r) • ${{ parameters.environment }} • ${{ coalesce(split(parameters.example, '/')[2], split(parameters.example, '/')[1]) }} • test

parameters:
- name: environment
  type: string
  default: TST-iac
  values:
  - TST-iac
- name: example
  type: string
  default: examples/01-default
  values:
  - examples/01-default
- name: timeout_in_minutes
  type: number
  default: 60
- name: no_proxy
  type: string
  default: '*.core.windows.net'
- name: terraformUnlockStateLockID
  type: string
  default: ' '

variables:
  - group: 'Centrally managed variable group'
  - template: env/${{ parameters.environment }}.yaml@tooling

resources:
  repositories:
    - repository: tooling
      type: git
      name: tooling
      ref: refs/tags/v2
    - repository: pipelinetemplates
      type: git
      name: OTPHU-CDO-ADOS-TOOLS/pipelinetemplates
      ref: refs/tags/v6

extends:
  template: iac-pipelines/iac-test.yaml@tooling
  parameters:
    environment: ${{ parameters.environment }}
    terraformProjectLocation: ${{ parameters.example }}
    terraformExtraNoProxy: ${{ parameters.no_proxy }}
    timeoutInMinutes: ${{ parameters.timeout_in_minutes }}
    terraformUnlockStateLockID: ${{ parameters.terraformUnlockStateLockID }}

    appCode: ${{ variables.appCode }}
    armServiceConnectionName: ${{ variables.armServiceConnectionName }}
    storageAccountResourceGroup: ${{ variables.storageAccountResourceGroup }}
    storageAccountName: ${{ variables.storageAccountName }}
    storageAccountContainerName: ${{ variables.storageAccountContainerName }}

    terraformVersion: 1.7.4
    #terraformRCFileForNetworkMirror: '$(Pipeline.Workspace)/source_repo/network-mirror/.terraformrc'