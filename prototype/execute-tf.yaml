parameters:
- name: folder
  type: string
  default: examples/01-default
- name: tf_command
  type: string
  default: plan
  values:
  - plan
  - apply
  - destroy
- name: timeout_in_minutes
  type: number
  default: 60
- name: no_proxy
  type: string
  default: ' '
trigger:
- none

resources:
  repositories:
    - repository: tooling
      type: git
      name: tooling

jobs:
- template: pipelines/terraform-module-execute.yaml@tooling
  parameters:
    folder: ${{ parameters.folder }}
    tf_command: ${{ parameters.tf_command }}
    timeout_in_miuntes: ${{ parameters.timeout_in_minutes }}
    no_proxy: ${{ parameters.no_proxy }}