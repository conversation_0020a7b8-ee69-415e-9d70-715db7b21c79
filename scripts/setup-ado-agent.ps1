mkdir C:\agent
cd C:\agent
Invoke-WebRequest -Uri "https://vstsagentpackage.azureedge.net/agent/3.220.5/vsts-agent-win-x64-3.220.5.zip" -OutFile "C:\agent\vsts-agent-win-x64-3.220.5.zip" -Proxy http://***********:8083
dir
Add-Type -AssemblyName System.IO.Compression.FileSystem ; [System.IO.Compression.ZipFile]::ExtractToDirectory("c:\agent\vsts-agent-win-x64-3.220.5.zip", "$PWD")
dir
.\config.cmd --unattended --url https://dev.azure.com/ADOS-OTPHU-01 --auth pat --token <pat> --pool DEV-win2019agent-Deploy --runAsService --agent win2019agent --runAsService --proxyurl http://***********:8083