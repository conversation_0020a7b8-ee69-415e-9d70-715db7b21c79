#!/bin/bash

# Check if the script is running in a Git repository
if ! git rev-parse --is-inside-work-tree > /dev/null 2>&1; then
    echo "Error: Not in a Git repository!"
    exit 1
fi

# Check if a major.minor parameter is provided (e.g., 2.3)
if [ -z "$1" ]; then
    echo "Usage: $0 <major.minor> [--dry-run]"
    echo "Example: $0 2.3 --dry-run"
    exit 1
fi

# Validate the input major.minor format
if ! echo "$1" | grep -E '^[0-9]+\.[0-9]+$' > /dev/null; then
    echo "Error: Invalid major.minor format. Example: 2.3"
    exit 1
fi

# Check for dry-run flag
dry_run=false
if [ "$2" == "--dry-run" ]; then
    dry_run=true
    echo "Running in dry-run mode. No changes will be applied."
fi

# Function to compare version numbers
version_compare() {
    local ver1=$1 ver2=$2
    if [ "$ver1" = "$ver2" ]; then
        return 0
    fi
    local IFS=.
    local v1=(${ver1#v}) v2=(${ver2#v})
    local v1_major=${v1[0]} v1_minor=${v1[1]:-0} v1_patch=${v1[2]:-0}
    local v2_major=${v2[0]} v2_minor=${v2[1]:-0} v2_patch=${v2[2]:-0}
    if [ "$v1_major" -lt "$v2_major" ]; then
        return 1
    elif [ "$v1_major" -gt "$v2_major" ]; then
        return 2
    fi
    if [ "$v1_minor" -lt "$v2_minor" ]; then
        return 1
    elif [ "$v1_minor" -gt "$v2_minor" ]; then
        return 2
    fi
    if [ "$v1_patch" -lt "$v2_patch" ]; then
        return 1
    elif [ "$v1_patch" -gt "$v2_patch" ]; then
        return 2
    fi
    return 0
}

# Function to strip only standard suffixes from a tag for comparison
strip_standard_suffix() {
    local tag=$1
    echo "$tag" | sed -E 's/(-obsolete|-deprecated|_deprecated)$//'
}

# Function to strip ALL suffixes from a tag for base version comparison
strip_all_suffixes() {
    local tag=$1
    echo "$tag" | sed -E 's/(-[A-Za-z0-9\._-]+)$//'
}

# Function to extract major.minor from a tag
get_major_minor() {
    local tag=$1
    echo "$tag" | sed -E 's/^v([0-9]+\.[0-9]+).*/\1/'
}

# Fetch all tags that look like vX.Y.Z, vX.Y, or have a suffix.
tags=$(git tag -l | grep -E '^v[0-9]+\.[0-9]+(\.[0-9]+)?([-_][A-Za-z0-9\._-]+)?$' | sort -V)

# Remove duplicates and sort
tags=$(echo "$tags" | tr ' ' '\n' | sort -V | uniq)

# Display current tag list
echo "Current tags in the repository:"
echo "-------------------------------"
for tag in $tags; do
    objecthash=$(git rev-parse "$tag" | cut -c1-8)
    commithash=$(git rev-parse "$tag^{}" | cut -c1-8)
    printf "%-20s - %s (%s)\n" "$tag" "$commithash" "$objecthash"
done
echo "-------------------------------"

# Exit if no valid tags are found
if [ -z "$tags" ]; then
    echo "Error: No valid vX.Y or vX.Y.Z tags found in the repository!"
    exit 1
fi

# Input major.minor version (without v prefix)
input_version=$1

# Identify non-standard suffixed tags that are duplicates of a standard tag by hash
delete_tags=""
keep_tags=""
for tag in $tags; do
    base_tag=$(strip_all_suffixes "$tag")

    # Check if the tag has a non-standard suffix
    if [[ "$tag" != "$base_tag" ]]; then
        # Check if a non-suffixed version of the same tag exists
        if echo "$tags" | grep -qE "^$base_tag$"; then
            # Get the commit hash for both tags
            hash_suffixed=$(git rev-parse "$tag")
            hash_base=$(git rev-parse "$base_tag")

            # Compare the hashes
            if [ "$hash_suffixed" == "$hash_base" ]; then
                delete_tags+="$tag\n"
                continue # Skip to the next tag
            fi
        fi
    fi
    # If the tag is not a duplicate by hash, add it to the keep_tags list
    keep_tags+="$tag\n"
done

# Determine the latest tag from the "kept" tags
latest_tag=""
latest_version="0.0.0"
while IFS= read -r tag; do
    [ -z "$tag" ] && continue
    # Use strip_all_suffixes for finding the latest version
    base_tag=$(strip_all_suffixes "$tag")
    version_compare "$base_tag" "v$latest_version"
    if [ $? -eq 2 ]; then
        latest_tag="$tag"
        latest_version="${base_tag#v}"
    fi
done < <(echo -e "$keep_tags" | sort -V)

# Determine the latest major.minor version
latest_major_minor=$(get_major_minor "$(strip_all_suffixes "$latest_tag")")

# Process tags to be kept for renaming or keeping as is
if [ "$dry_run" = true ]; then
    echo "Planned tag changes:"
    echo "-------------------------------"
fi

while IFS= read -r tag; do
    [ -z "$tag" ] && continue

    # Use strip_standard_suffix here to preserve non-standard suffixes like -B1
    tag_without_standard_suffix=$(strip_standard_suffix "$tag")
    base_tag=$(strip_all_suffixes "$tag")
    tag_major_minor=$(get_major_minor "$base_tag")

    version_compare "v$tag_major_minor" "v$input_version"
    if [ $? -le 1 ]; then
        # Append -obsolete to the tag that already has its non-standard suffixes
        new_tag="${tag_without_standard_suffix}-obsolete"
        if [ "$new_tag" != "$tag" ]; then
            if [ "$dry_run" = true ]; then
                echo "Would rename tag: $tag -> $new_tag"
            else
                git tag "$new_tag" "$tag"
                git tag -d "$tag"
                git push origin "$new_tag"
                git push origin :"$tag"
            fi
        elif [ "$dry_run" = true ]; then
            echo "Tag ($tag) already has correct suffix, no change needed."
        fi
    else
        if [ "$dry_run" = true ]; then
            echo "Tag ($tag) will remain unchanged."
        fi
    fi
done < <(echo -e "$keep_tags" | sort -V)

# Final dry-run/real deletion of tags
if [ "$dry_run" = true ]; then
    while IFS= read -r tag; do
        [ -z "$tag" ] && continue
        echo "Would delete tag: $tag (duplicate with suffix)"
    done < <(echo -e "$delete_tags" | sort -u)
    echo "-------------------------------"
    echo "Dry-run completed. No changes were applied."
else
    echo "-------------------------------"
    echo "Applying changes..."
    while IFS= read -r tag; do
        [ -z "$tag" ] && continue
        echo "Deleting duplicate tag: $tag"
        git tag -d "$tag"
        git push origin :"$tag"
    done < <(echo -e "$delete_tags" | sort -u)
    echo "Tag maintenance completed."
fi