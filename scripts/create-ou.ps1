# Installs the required cmdlets
#Install-WindowsFeature -Name AD-Domain-Services -IncludeManagementTools

$password = ConvertTo-SecureString "<password>" -AsPlainText -Force
$cred = New-Object System.Management.Automation.PSCredential ("svc_dev_az_adds", $password)

# New org unit
New-ADOrganizationalUnit -Credential $cred -Server corp.otpbank.hu -Name "test01" -Path "OU=dev,OU=Azure,OU=PublicCloud,DC=corp,DC=otpbank,DC=hu"
# delete org unit
Get-ADOrganizationalUnit -Credential $cred -server corp.otpbank.hu -identity "OU=Compute,OU=test,OU=dev,OU=Azure,OU=PublicCloud,DC=corp,DC=otpbank,DC=hu" | Set-ADObject -ProtectedFromAccidentalDeletion:$false -PassThru | Remove-ADOrganizationalUnit -Confirm:$false